---
// CompetitorComparison.astro - Competitor comparison table
const features = [
  {
    feature: "Custom widget, trained on your site",
    clearbeam: true,
    chatbase: false,
    botpress: "complex",
    tidio: true
  },
  {
    feature: "Human-like assistant behavior",
    clearbeam: true,
    chatbase: "basic",
    botpress: true,
    tidio: false
  },
  {
    feature: "Calendly/Google Calendar scheduling",
    clearbeam: true,
    chatbase: false,
    botpress: false,
    tidio: false
  },
  {
    feature: "Form guidance & live field entry",
    clearbeam: true,
    chatbase: false,
    botpress: false,
    tidio: false
  },
  {
    feature: "Full brand design",
    clearbeam: true,
    chatbase: false,
    botpress: "dev needed",
    tidio: false
  },
  {
    feature: "Works like a sales agent",
    clearbeam: true,
    chatbase: false,
    botpress: false,
    tidio: false
  },
  {
    feature: "Affordable & flexible pricing",
    clearbeam: true,
    chatbase: false,
    botpress: false,
    tidio: false
  }
];
---

<section class="py-16 md:py-20 bg-white px-4 md:px-6">
  <div class="max-w-6xl mx-auto">
    <h2 class="text-3xl md:text-4xl font-bold text-center text-[#20232D] mb-4">
      How We Compare
    </h2>
    <p class="text-center text-gray-600 mb-12 max-w-3xl mx-auto">
      See why businesses choose Clearbeam over other chatbot solutions
    </p>

    <!-- Desktop Table -->
    <div class="hidden md:block overflow-x-auto bg-white rounded-lg shadow-lg border border-gray-200">
      <table class="w-full">
        <thead>
          <tr class="bg-[#F7F8F9] border-b border-gray-200">
            <th class="text-left py-4 px-6 font-semibold text-[#20232D]">Feature / Service</th>
            <th class="text-center py-4 px-4 font-semibold text-[#3D84F3]">
              <div class="flex flex-col items-center">
                <span>Clearbeam</span>
              </div>
            </th>
            <th class="text-center py-4 px-4 font-semibold text-gray-600">
              <div class="flex flex-col items-center">
                <span>Chatbase</span>
              </div>
            </th>
            <th class="text-center py-4 px-4 font-semibold text-gray-600">
              <div class="flex flex-col items-center">
                <span>Botpress</span>
              </div>
            </th>
            <th class="text-center py-4 px-4 font-semibold text-gray-600">
              <div class="flex flex-col items-center">
                <span>Tidio</span>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {features.map((row, index) => (
            <tr class={`border-b border-gray-100 ${index % 2 === 0 ? 'bg-white' : 'bg-[#FAFAFA]'}`}>
              <td class="py-4 px-6 font-medium text-[#20232D]">{row.feature}</td>
              <td class="text-center py-4 px-4">
                <span class="text-2xl text-[#3D84F3]">✅</span>
              </td>
              <td class="text-center py-4 px-4">
                {row.chatbase === true ? (
                  <span class="text-2xl text-green-500">✅</span>
                ) : row.chatbase === "basic" ? (
                  <span class="text-sm text-yellow-600 font-medium">✅ (basic)</span>
                ) : (
                  <span class="text-2xl text-red-500">❌</span>
                )}
              </td>
              <td class="text-center py-4 px-4">
                {row.botpress === true ? (
                  <span class="text-2xl text-green-500">✅</span>
                ) : row.botpress === "complex" ? (
                  <span class="text-sm text-yellow-600 font-medium">✅ (complex UI)</span>
                ) : row.botpress === "dev needed" ? (
                  <span class="text-sm text-yellow-600 font-medium">✅ (dev needed)</span>
                ) : (
                  <span class="text-2xl text-red-500">❌</span>
                )}
              </td>
              <td class="text-center py-4 px-4">
                {row.tidio === true ? (
                  <span class="text-2xl text-green-500">✅</span>
                ) : (
                  <span class="text-2xl text-red-500">❌</span>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>

    <!-- Mobile Cards -->
    <div class="md:hidden space-y-6">
      {features.map((row) => (
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
          <h3 class="font-semibold text-[#20232D] mb-4">{row.feature}</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
              <div class="font-medium text-[#3D84F3] mb-2">Clearbeam</div>
              <span class="text-2xl text-[#3D84F3]">✅</span>
            </div>
            <div class="text-center">
              <div class="font-medium text-gray-600 mb-2">Others</div>
              <span class="text-2xl text-red-500">❌</span>
            </div>
          </div>
        </div>
      ))}
    </div>

    <!-- CTA Section -->
    <div class="text-center mt-12">
      <h3 class="text-2xl font-bold text-[#20232D] mb-4">Ready to get started?</h3>
      <p class="text-gray-600 mb-6">Join businesses that chose the smarter solution</p>
      <a href="/contact" class="inline-block bg-[#3D84F3] text-white py-3 px-8 rounded-lg hover:bg-[#2D74E3] transition-colors duration-300 font-medium">
        Get Your Custom Quote
      </a>
    </div>
  </div>
</section>