<section id="faqs" class="w-full text-center py-16 my-[10vh] bg-inherit -z-10">
  <div class="max-w-4xl mx-auto px-4">
    <h2 class="text-4xl font-bold text-[#20232D] mb-12">
      Frequently Asked Questions
    </h2>

    <div
      x-data="{ 
      activeAccordion: '', 
      setActiveAccordion(id) { 
          this.activeAccordion = (this.activeAccordion == id) ? '' : id 
      } 
    }"
      class="relative w-full mx-auto overflow-hidden text-[1.1rem] font-normal bg-[#FFFFFF] divide-y divide-gray-200 rounded-md my-[10vh] text-left"
      style="box-shadow: 0px 0px 10px #20232D45;"
    >
      <div x-data="{ id: $id('accordion') }" class="cursor-pointer group">
        <button
          @click="setActiveAccordion(id)"
          class="flex items-center justify-between w-full p-4 text-left select-none"
        >
          <span class="font-semibold"
            >What's included in the setup fee?</span
          >
          <svg
            class="w-4 h-4 duration-200 ease-out"
            :class="{ 'rotate-180': activeAccordion==id }"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><polyline points="6 9 12 15 18 9"></polyline></svg
          >
        </button>
        <div x-show="activeAccordion==id" x-collapse x-cloak>
          <div class="p-4 pt-0 opacity-70">
            The setup fee covers custom AI widget design, branding, integration with your website, AI training on your content, and initial configuration. Pro setup includes additional features like analytics dashboard, form integration, and ongoing consultation.
          </div>
        </div>
      </div>
      
      <div x-data="{ id: $id('accordion') }" class="cursor-pointer group">
        <button
          @click="setActiveAccordion(id)"
          class="flex items-center justify-between w-full p-4 text-left select-none"
        >
          <span class="font-semibold"
            >Can I train the AI on my specific business information?</span
          >
          <svg
            class="w-4 h-4 duration-200 ease-out"
            :class="{ 'rotate-180': activeAccordion==id }"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><polyline points="6 9 12 15 18 9"></polyline></svg
          >
        </button>
        <div x-show="activeAccordion==id" x-collapse x-cloak>
          <div class="p-4 pt-0 opacity-70">
            Absolutely! We train your AI using your website content, FAQs, product information, and any additional business knowledge you provide. This ensures accurate, brand-specific responses that truly represent your business.
          </div>
        </div>
      </div>
      
      <div x-data="{ id: $id('accordion') }" class="cursor-pointer group">
        <button
          @click="setActiveAccordion(id)"
          class="flex items-center justify-between w-full p-4 text-left select-none"
        >
          <span class="font-semibold"
            >What's the difference between Core and Plus monthly plans?</span
          >
          <svg
            class="w-4 h-4 duration-200 ease-out"
            :class="{ 'rotate-180': activeAccordion==id }"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><polyline points="6 9 12 15 18 9"></polyline></svg
          >
        </button>
        <div x-show="activeAccordion==id" x-collapse x-cloak>
          <div class="p-4 pt-0 opacity-70">
            Core ($29/month) includes hosting, monitoring, basic updates, and standard support. Plus ($69/month) adds priority support, custom feature requests, AI prompt tuning, analytics reports, and monthly optimization calls.
          </div>
        </div>
      </div>
      
      <div x-data="{ id: $id('accordion') }" class="cursor-pointer group">
        <button
          @click="setActiveAccordion(id)"
          class="flex items-center justify-between w-full p-4 text-left select-none"
        >
          <span class="font-semibold"
            >How quickly can you implement the AI widget?</span
          >
          <svg
            class="w-4 h-4 duration-200 ease-out"
            :class="{ 'rotate-180': activeAccordion==id }"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><polyline points="6 9 12 15 18 9"></polyline></svg
          >
        </button>
        <div x-show="activeAccordion==id" x-collapse x-cloak>
          <div class="p-4 pt-0 opacity-70">
            Starter setup typically takes 3-5 business days, while Pro setup takes 7-10 business days due to additional customization and integrations. We'll provide a detailed timeline during your consultation.
          </div>
        </div>
      </div>
      
      <div x-data="{ id: $id('accordion') }" class="cursor-pointer group">
        <button
          @click="setActiveAccordion(id)"
          class="flex items-center justify-between w-full p-4 text-left select-none"
        >
          <span class="font-semibold">Can the AI schedule appointments and capture leads?</span>
          <svg
            class="w-4 h-4 duration-200 ease-out"
            :class="{ 'rotate-180': activeAccordion==id }"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><polyline points="6 9 12 15 18 9"></polyline></svg
          >
        </button>
        <div x-show="activeAccordion==id" x-collapse x-cloak>
          <div class="p-4 pt-0 opacity-70">
            Yes! Our AI can integrate with Calendly or Google Calendar for appointment scheduling, capture lead information through forms, and even guide users through multi-step processes. It works like a dedicated sales agent for your business.
          </div>
        </div>
      </div>
      
      <div x-data="{ id: $id('accordion') }" class="cursor-pointer group">
        <button
          @click="setActiveAccordion(id)"
          class="flex items-center justify-between w-full p-4 text-left select-none"
        >
          <span class="font-semibold"
            >What add-ons are available and how much do they cost?</span
          >
          <svg
            class="w-4 h-4 duration-200 ease-out"
            :class="{ 'rotate-180': activeAccordion==id }"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><polyline points="6 9 12 15 18 9"></polyline></svg
          >
        </button>
        <div x-show="activeAccordion==id" x-collapse x-cloak>
          <div class="p-4 pt-0 opacity-70">
            We offer CRM integrations (HubSpot, Notion, Airtable), email automation, external API connections, additional widgets, and custom workflows. Pricing varies based on complexity - contact us for a detailed quote based on your specific needs.
          </div>
        </div>
      </div>
    </div>
  </div>
</section>