---
// ChatWidget.astro - Enhanced floating chat widget with form assistance and Calendly integration
import { gsap } from "gsap";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";

// Register GSAP plugins at the component level
if (typeof gsap !== 'undefined') {
  gsap.registerPlugin(ScrollToPlugin);
}
---

<div
  x-data="{ 
    open: false, 
    message: '', 
    messages: [{ role: 'assistant', content: 'Hey there! 👋 How can I assist you today?' }],
    isTyping: false,
    isWaitingForResponse: false,
    isResizing: false,
    startX: 0,
    startY: 0,
    startWidth: 0,
    startHeight: 0,
    modelIndex: 2, // Default to DeepSeek R1 0528 Qwen3 8B
    modelNames: [
      'Llama 3.3 8B Instruct',
      'DeepSeek R1 0528',
      'DeepSeek R1 0528 Qwen3 8B'
    ],
    showSuggestions: true,
    isInFormMode: false,
    currentFormField: null,
    formData: {},
    
    init() {
      // Make Alpine.js component accessible to window for external control
      if (window.Alpine && !window._chatWidgetInitialized) {
        window._chatWidgetInitialized = true;
        window.chatWidgetComponent = this;
      }
      
      // Watch for open state changes
      this.$watch('open', value => {
        if (value) {
          // When opening the chat
          setTimeout(() => {
            if (this.$refs && this.$refs.chatInput) {
              this.$refs.chatInput.focus()
            }
            // Animate initial message when chat is opened
            const initialMessage = this.$refs.messages.querySelector('.message-container')
            if (initialMessage && window.animateMessage) {
              window.animateMessage(initialMessage)
            }
            // Show suggestions after initial message
            setTimeout(() => {
              this.showSuggestions = true;
              if (window.animateSuggestions) {
                window.animateSuggestions();
              }
            }, 800);
          }, 100)
        } else {
          // When closing the chat
          this.isTyping = false;
          this.isWaitingForResponse = false;
          this.showSuggestions = true; // Reset suggestions
          this.isInFormMode = false;
          this.currentFormField = null;
          
          // Reset message input but keep conversation history
          this.message = '';
        }
      })
      
      // Add watcher for isWaitingForResponse
      this.$watch('isWaitingForResponse', value => {
        if (!value && this.open) {
          // When response cooldown ends, focus the input
          setTimeout(() => {
            if (this.$refs && this.$refs.chatInput) {
              this.$refs.chatInput.focus();
            }
          }, 100);
        }
      });
    },
    
    handleSuggestion(suggestionText) {
      if (this.isWaitingForResponse) return;
      
      // Hide suggestions
      this.showSuggestions = false;
      
      // Add user message
      this.messages.push({ role: 'user', content: suggestionText });
      this.isWaitingForResponse = true;
      
      // Animate the new user message
      this.$nextTick(() => {
        const messagesDiv = this.$refs.messages;
        const newMessage = messagesDiv.querySelector('.message-container:last-child');
        if (newMessage && window.animateMessage) {
          window.animateMessage(newMessage);
        }
        if (window.smoothScrollToBottom) {
          window.smoothScrollToBottom(messagesDiv);
        }
        
        // Show typing indicator after user message
        setTimeout(() => {
          this.isTyping = true;
          if (window.showTypingIndicator) {
            window.showTypingIndicator();
          }
        }, 400);
      });

      // Send to API
      this.sendToAPI();
    },
    
    sendMessage() {
      if (!this.message.trim() || this.isWaitingForResponse) return
      
      // Hide suggestions when user starts typing their own message
      this.showSuggestions = false;
      
      // Add user message
      this.messages.push({ role: 'user', content: this.message })
      const userMessage = this.message
      this.message = ''
      this.isWaitingForResponse = true
      
      // Scroll to bottom and animate the new user message
      this.$nextTick(() => {
        const messagesDiv = this.$refs.messages
        const newMessage = messagesDiv.querySelector('.message-container:last-child')
        if (newMessage && window.animateMessage) {
          window.animateMessage(newMessage)
        }
        if (window.smoothScrollToBottom) {
          window.smoothScrollToBottom(messagesDiv)
        }
        
        // Show typing indicator after user message is animated
        setTimeout(() => {
          this.isTyping = true
          if (window.showTypingIndicator) {
            window.showTypingIndicator()
          }
        }, 400)
      })

      this.sendToAPI();
    },
    
    sendToAPI() {
      // Send to API with form context if in form mode
      const contextualMessages = this.isInFormMode ? [
        ...this.messages,
        { 
          role: 'system', 
          content: `User is currently filling out the demo request form. Current form data: ${JSON.stringify(this.formData)}. Current field: ${this.currentFormField}. Help them with form-related questions and guide them back to the form when appropriate.` 
        }
      ] : this.messages;

      fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messages: contextualMessages,
          modelIndex: this.modelIndex,
          formMode: this.isInFormMode,
          formData: this.formData
        })
      })
      .then(res => res.json())
      .then(data => {
        const reply = data.choices?.[0]?.message?.content || 'Something went wrong.'
        
        // Hide typing indicator before showing the response
        this.isTyping = false
        if (window.hideTypingIndicator) {
          window.hideTypingIndicator(() => {
            // Add AI response after typing indicator is hidden
            setTimeout(() => {
              this.messages.push({ role: 'assistant', content: reply })
              
              // Check if AI wants to trigger form or calendar actions
              this.handleAIActions(reply);
              
              // Animate the new AI message
              this.$nextTick(() => {
                const messagesDiv = this.$refs.messages
                const newMessage = messagesDiv.querySelector('.message-container:last-child')
                if (newMessage && window.animateMessage) {
                  window.animateMessage(newMessage)
                }
                if (window.smoothScrollToBottom) {
                  window.smoothScrollToBottom(messagesDiv)
                }
                
                // Re-enable input after message is added
                this.isWaitingForResponse = false
                
                // Focus input after response is complete
                if (this.$refs && this.$refs.chatInput) {
                  this.$refs.chatInput.focus()
                }
              })
            }, 200)
          })
        }
      })
      .catch(err => {
        console.error('API request error:', err)
        this.isTyping = false
        this.isWaitingForResponse = false
        this.messages.push({ role: 'assistant', content: 'Failed to reach AI. Please try again later.' })
        
        // Animate error message
        this.$nextTick(() => {
          const messagesDiv = this.$refs.messages
          const newMessage = messagesDiv.querySelector('.message-container:last-child')
          if (newMessage && window.animateMessage) {
            window.animateMessage(newMessage)
          }
          if (window.smoothScrollToBottom) {
            window.smoothScrollToBottom(messagesDiv)
          }
        })
      })
    },

    handleAIActions(reply) {
      // Check for form-related actions
      if (reply.includes('🡩 Return to Form') || reply.includes('return to form')) {
        // Add a button to return to form
        setTimeout(() => {
          this.addActionButton('Return to Form', () => {
            if (window.contactForm) {
              window.contactForm.scrollToForm();
              this.open = false; // Close chat when returning to form
            }
          });
        }, 1000);
      }

      // Check for calendar scheduling actions
      if (reply.includes('schedule') && reply.includes('appointment')) {
        setTimeout(() => {
          this.addActionButton('Schedule Call', () => {
            if (window.showCalendly) {
              window.showCalendly();
            }
          });
        }, 1000);
      }
    },

    addActionButton(text, action) {
      const messagesDiv = this.$refs.messages;
      const buttonContainer = document.createElement('div');
      buttonContainer.className = 'flex justify-start mt-2 mb-4';
      buttonContainer.innerHTML = `
        <button class='bg-[#3D84F3] text-white px-4 py-2 rounded-lg hover:bg-[#2D74E3] transition-colors duration-200 text-sm font-medium'>
          ${text}
        </button>
      `;
      
      buttonContainer.querySelector('button').addEventListener('click', action);
      messagesDiv.appendChild(buttonContainer);
      
      if (window.smoothScrollToBottom) {
        window.smoothScrollToBottom(messagesDiv);
      }
    },
    
    startResize(e) {
      this.isResizing = true
      this.startX = e.clientX
      this.startY = e.clientY
      
      const chatBox = this.$refs.chatBox
      this.startWidth = chatBox.offsetWidth
      this.startHeight = chatBox.offsetHeight
      
      const resizeHandler = this.resize.bind(this)
      const stopResizeHandler = this.stopResize.bind(this)
      
      document.addEventListener('mousemove', resizeHandler)
      document.addEventListener('mouseup', stopResizeHandler)
      
      // Store handlers for cleanup
      this._resizeHandler = resizeHandler;
      this._stopResizeHandler = stopResizeHandler;
    },
    
    resize(e) {
      if (!this.isResizing) return
      
      const chatBox = this.$refs.chatBox
      const width = this.startWidth - (e.clientX - this.startX)
      const height = this.startHeight - (e.clientY - this.startY)
      
      chatBox.style.width = `${Math.max(320, width)}px`
      chatBox.style.height = `${Math.max(400, height)}px`
    },
    
    stopResize() {
      this.isResizing = false
      if (this._resizeHandler) {
        document.removeEventListener('mousemove', this._resizeHandler)
      }
      if (this._stopResizeHandler) {
        document.removeEventListener('mouseup', this._stopResizeHandler)
      }
    }
  }"
  class="fixed bottom-4 z-50 duration-300 ease right-[-60px]"
  id="widget"
>
  <!-- Chat Box -->
  <div x-show="open" 
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0 transform translate-y-4"
    x-transition:enter-end="opacity-100 transform translate-y-0"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100 transform translate-y-0"
    x-transition:leave-end="opacity-0 transform translate-y-4"
    class="absolute bottom-16 right-0 w-96 bg-white shadow-xl rounded-lg flex flex-col overflow-hidden border border-[#DCDCDC]"
    style="min-height: 450px; min-width: 320px; max-width: 90vw; max-height: 80vh; height: 500px;"
    x-ref="chatBox">
    
    <!-- Header with resize handle and model selector -->
    <div class="flex items-center justify-between px-4 py-3 border-b border-[#DCDCDC] relative bg-white">
      <!-- Custom resize handle (only visible on desktop) -->
      <div 
        class="resize-handle hidden md:block cursor-nwse-resize absolute top-3 left-3 text-[#1E232E] hover:text-gray-600 transition-colors z-10" 
        x-ref="resizeHandle"
        @mousedown="startResize($event)">
        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22 2L2 22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          <path d="M16 2L2 16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          <path d="M10 2L2 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </div>
      
      <!-- Remove model selector dropdown and its container -->
      
      <!-- Title centered - adjust to take full width -->
      <div class="flex-grow flex justify-center">
        <span class="text-sm font-semibold text-[#1E232E]">CB Assistant</span>
      </div>
      
      <!-- Close button -->
      <button @click="open = false" class="text-[#1E232E] hover:text-gray-600 transition-colors flex-shrink-0">
        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0.988873 6.95887C0.713231 7.23906 0.697766 7.75668 0.99888 8.05234C1.29908 8.35254 1.81216 8.34345 2.09326 8.06235L4.87152 5.28318L7.64614 8.0578C7.93725 8.35345 8.43941 8.3489 8.73506 8.04779C8.87837 7.90336 8.95954 7.70863 8.96124 7.50518C8.96294 7.30173 8.88504 7.10567 8.74416 6.95887L5.97227 4.18425L8.74689 1.40417C8.88776 1.25737 8.96567 1.06131 8.96397 0.857855C8.96227 0.654401 8.8811 0.459673 8.73779 0.315245C8.59333 0.171666 8.3984 0.090326 8.19473 0.0886259C7.99106 0.0869258 7.7948 0.165001 7.64796 0.306148L4.87152 3.07986L2.09326 0.29978C1.81125 0.0241372 1.29454 0.00867205 0.99888 0.308877C0.703224 0.609991 0.713231 1.12307 0.989783 1.40326L3.76895 4.18334L0.988873 6.95796V6.95887Z" fill="currentColor" />
        </svg>
      </button>
    </div>

    <!-- Messages Area -->
    <div
      x-ref="messages"
      class="flex-1 p-4 overflow-y-auto text-sm text-[#1E232E] scrollbar-hide relative"
    >
      <div class="flex flex-col space-y-3">
        <template x-for="(message, index) in messages" :key="index">
          <div
            :class="{
              'self-start max-w-[70%]': message.role === 'assistant',
              'self-end max-w-[70%]': message.role === 'user'
            }"
            class="message-container"
          >
            <div
              :class="{
              'bg-[#1E232E] text-[#F8F8FA]': message.role === 'assistant',
              'bg-[#F8F8FA] border border-[#DCDCDC]': message.role === 'user'
            }"
              class="p-3 rounded-lg break-words"
            >
              <span x-html="message.content ? window.formatMessage(message.content) : ''"></span>
            </div>
          </div>
        </template>
      </div>
      
      <!-- Typing indicator positioned absolutely -->
      <div
        x-show="isTyping"
        x-cloak
        class="typing-indicator bg-gray-200 p-3 rounded-lg flex space-x-1 items-center max-w-[70%] absolute"
        id="typing-indicator"
      >
        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
        <div
          class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
          style="animation-delay: 0.2s"
        >
        </div>
        <div
          class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
          style="animation-delay: 0.4s"
        >
        </div>
      </div>
    </div>

    <!-- Chat Suggestions -->
    <div 
      x-show="showSuggestions && messages.length === 1" 
      x-transition:enter="transition ease-out duration-300 delay-300"
      x-transition:enter-start="opacity-0 transform translate-y-4"
      x-transition:enter-end="opacity-100 transform translate-y-0"
      x-transition:leave="transition ease-in duration-200"
      x-transition:leave-start="opacity-100 transform translate-y-0"
      x-transition:leave-end="opacity-0 transform translate-y-4"
      class="px-4 pb-2 space-y-2 suggestions-container"
    >
      <button 
        @click="handleSuggestion('Explain pricing options')"
        :disabled="isWaitingForResponse"
        class="w-full text-left p-3 bg-[#E6F3FF] hover:bg-[#D1E9FF] text-[#2D3748] rounded-lg border border-[#B8E0FF] transition-colors duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed suggestion-box"
      >
        💰 Explain pricing options
      </button>
      <button 
        @click="handleSuggestion('How does it work?')"
        :disabled="isWaitingForResponse"
        class="w-full text-left p-3 bg-[#E6F3FF] hover:bg-[#D1E9FF] text-[#2D3748] rounded-lg border border-[#B8E0FF] transition-colors duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed suggestion-box"
      >
        ⚙️ How does it work?
      </button>
      <button 
        @click="handleSuggestion('Help me fill out the demo form')"
        :disabled="isWaitingForResponse"
        class="w-full text-left p-3 bg-[#E6F3FF] hover:bg-[#D1E9FF] text-[#2D3748] rounded-lg border border-[#B8E0FF] transition-colors duration-200 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed suggestion-box"
      >
        📝 Help me fill out the demo form
      </button>
    </div>

    <!-- Input Area -->
    <form @submit.prevent="sendMessage" class="flex items-center px-2 py-2 border-t border-[#DCDCDC]">
      <div class="flex-1 relative">
        <input type="text" 
          x-model="message" 
          placeholder="Type a message..."
          @keydown.enter.prevent="sendMessage"
          x-ref="chatInput"
          :disabled="isWaitingForResponse"
          class="w-full px-3 py-2 pr-10 text-sm rounded-md bg-[#F8F8FA] border border-[#DCDCDC] focus:outline-none focus:ring-2 focus:ring-[#3D84F3] text-[#1E232E] disabled:opacity-70 disabled:cursor-not-allowed transition-all duration-300"
          style="overflow-x: auto; white-space: nowrap; text-overflow: ellipsis;"
          @input="$event.target.scrollLeft = $event.target.scrollWidth">
        <button type="submit" 
          :disabled="isWaitingForResponse || !message.trim()"
          :class="{'opacity-50 cursor-not-allowed': isWaitingForResponse || !message.trim()}"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-[#1E232E] hover:text-[#3D84F3] transition-colors">
          <img src="/src/assets/send_chat.svg" alt="Send" width="22" class="transition-transform duration-300 ease-in-out hover:scale-110" />
        </button>
      </div>
    </form>
  </div>

  <!-- Chat Toggle Button -->
  <button x-show="true" @click="open = !open"
    class="duration-150 ease focus:outline-none cursor-pointer rounded-full bg-white hover:shadow-xl p-1.5"
    style="box-shadow: 00px 00px 10px #00000044;"
    aria-label="Open chat">
    <img src="/src/assets/chat_button.svg" alt="Chat Button" />
  </button>
</div>

<script>
  // Ensure GSAP plugins are registered on the client side as well
  if (typeof gsap !== 'undefined') {
    gsap.registerPlugin(ScrollToPlugin);
    console.log("GSAP and ScrollToPlugin registered successfully");
  } else {
    console.warn("GSAP is not available");
  }

  document.addEventListener("DOMContentLoaded", function() {
    const widget = document.getElementById("widget");
    let widgetAlpineComponent = null;

    // Get Alpine.js component instance once it's initialized
    if (widget) {
      // Wait for Alpine to initialize
      setTimeout(() => {
        if (window.Alpine) {
          widgetAlpineComponent = window.Alpine.$data(widget);
          console.log("Alpine component initialized");
        }
      }, 100);
    }

    // Function to handle scroll events with proper hiding logic
    function handleScroll() {
      if (widget) {
        // Check if we're on the home page
        const isHomePage = window.location.pathname === '/' || window.location.pathname === '/index.html';
        
        if (isHomePage) {
          // Only apply sliding behavior on home page
          if (window.scrollY <= 50) {
            widget.style.right = "-60px";
            widget.style.opacity = "0";
            widget.style.pointerEvents = "none";
            
            // Close the widget when it slides off-screen
            if (widgetAlpineComponent && widgetAlpineComponent.open) {
              widgetAlpineComponent.open = false;
              console.log("Widget closed due to scroll position");
            }
          } else {
            widget.style.right = "20px";
            widget.style.opacity = "1";
            widget.style.pointerEvents = "auto";
          }
        } else {
          // On all other pages, keep the widget stationary
          widget.style.right = "20px";
          widget.style.opacity = "1";
          widget.style.pointerEvents = "auto";
        }
      }
    }

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Call initially

    // Enhanced function to animate messages with smooth slide-up and fade-in
    window.animateMessage = function(element) {
      if (!element) {
        console.warn("animateMessage: No element provided");
        return;
      }
      
      if (typeof gsap !== 'undefined') {
        try {
          // Set initial state - start from below and invisible
          gsap.set(element, { 
            opacity: 0, 
            y: 30, // Start further down for more noticeable slide
            scale: 0.9, // Slight scale for extra polish
            transformOrigin: "bottom center"
          });
          
          // Animate in with smooth slide-up and fade-in
          gsap.to(element, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.5, // Slightly faster for snappier feel
            ease: "power2.out", // Smooth easing
            delay: 0.1 // Small delay for staggered effect
          });
          console.log("Message slide-up animation applied");
        } catch (error) {
          console.error("GSAP animation error:", error);
          // Fallback if GSAP fails
          element.style.opacity = '1';
          element.style.transform = 'translateY(0) scale(1)';
        }
      } else {
        // CSS fallback animation
        element.style.transition = 'all 0.5s ease-out';
        element.style.opacity = '1';
        element.style.transform = 'translateY(0) scale(1)';
        console.warn("GSAP not available, using CSS fallback");
      }
    };

    // Function to animate suggestion boxes
    window.animateSuggestions = function() {
      const suggestions = document.querySelectorAll('.suggestion-box');
      if (suggestions.length === 0) return;
      
      if (typeof gsap !== 'undefined') {
        try {
          gsap.set(suggestions, { opacity: 0, y: 20, scale: 0.95 });
          gsap.to(suggestions, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.4,
            ease: "back.out(1.2)",
            stagger: 0.1
          });
          console.log("Suggestion boxes animated");
        } catch (error) {
          console.error("GSAP suggestion animation error:", error);
          suggestions.forEach(suggestion => {
            suggestion.style.opacity = '1';
            suggestion.style.transform = 'translateY(0) scale(1)';
          });
        }
      } else {
        suggestions.forEach((suggestion, index) => {
          suggestion.style.transition = `all 0.4s ease-out ${index * 0.1}s`;
          suggestion.style.opacity = '1';
          suggestion.style.transform = 'translateY(0) scale(1)';
        });
      }
    };

    // Function to smoothly scroll to bottom
    window.smoothScrollToBottom = function(container) {
      if (!container) {
        console.warn("smoothScrollToBottom: No container provided");
        return;
      }

      if (typeof gsap !== 'undefined' && gsap.getProperty && gsap.to) {
        try {
          gsap.to(container, {
            scrollTo: { y: "max", offsetY: 0 },
            duration: 0.6, // Slightly faster scroll
            ease: "power2.out",
          });
          console.log("Smooth scroll applied");
        } catch (error) {
          console.error("GSAP scroll error:", error);
          // Fallback if GSAP fails
          container.scrollTop = container.scrollHeight;
        }
      } else {
        container.scrollTop = container.scrollHeight;
        console.warn("ScrollToPlugin not available, using standard scrollTop");
      }
    };

    // Enhanced function to show typing indicator
    window.showTypingIndicator = function() {
      const indicator = document.getElementById("typing-indicator");
      const messagesContainer = document.querySelector('[x-ref="messages"]');
      
      if (!indicator || !messagesContainer) {
        console.warn("showTypingIndicator: Required elements not found");
        return;
      }
      
      // Position the typing indicator at the bottom of the messages
      const messagesDiv = messagesContainer.querySelector('.flex.flex-col.space-y-3');
      if (messagesDiv) {
        const messagesHeight = messagesDiv.scrollHeight;
        indicator.style.top = `${messagesHeight + 16}px`; // 16px spacing after last message
        indicator.style.left = '16px';
      }
      
      if (typeof gsap !== 'undefined') {
        try {
          gsap.killTweensOf(indicator); // Kill any existing animations
          gsap.set(indicator, { opacity: 0, y: 20, scale: 0.8 });
          gsap.to(indicator, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.4,
            ease: "back.out(1.2)"
          });
          
          // Scroll to show the typing indicator
          setTimeout(() => {
            window.smoothScrollToBottom(messagesContainer);
          }, 100);
          
          console.log("Typing indicator shown with animation");
        } catch (error) {
          console.error("GSAP typing indicator show error:", error);
          // Fallback if GSAP fails
          indicator.style.opacity = '1';
          indicator.style.transform = 'translateY(0) scale(1)';
        }
      } else {
        indicator.style.transition = 'all 0.4s ease-out';
        indicator.style.opacity = '1';
        indicator.style.transform = 'translateY(0) scale(1)';
        console.warn("GSAP not available for typing indicator, using CSS fallback");
      }
    };

    // Enhanced function to hide typing indicator
    window.hideTypingIndicator = function(callback) {
      const indicator = document.getElementById("typing-indicator");
      if (!indicator) {
        console.warn("hideTypingIndicator: No indicator found");
        if (callback) callback();
        return;
      }
      
      if (typeof gsap !== 'undefined') {
        try {
          gsap.killTweensOf(indicator); // Kill any existing animations
          gsap.to(indicator, {
            opacity: 0,
            y: 20,
            scale: 0.8,
            duration: 0.3,
            ease: "power2.in",
            onComplete: function() {
              if (callback) callback();
            }
          });
          console.log("Typing indicator hidden with animation");
        } catch (error) {
          console.error("GSAP typing indicator hide error:", error);
          // Fallback if GSAP fails
          indicator.style.opacity = '0';
          indicator.style.transform = 'translateY(20px) scale(0.8)';
          if (callback) setTimeout(callback, 300);
        }
      } else {
        indicator.style.transition = 'all 0.3s ease-in';
        indicator.style.opacity = '0';
        indicator.style.transform = 'translateY(20px) scale(0.8)';
        if (callback) setTimeout(callback, 300);
        console.warn("GSAP not available for hiding typing indicator, using CSS fallback");
      }
    };
  });
</script>

<style>
  /* Smooth scrolling for messages container */
  [x-ref="messages"] {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Enhanced message container styling for animations */
  .message-container {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    /* Start with invisible state for animation */
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }

  /* Typing indicator enhanced styling */
  .typing-indicator {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    z-index: 10;
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    position: absolute;
  }

  /* Suggestion boxes styling */
  .suggestion-box {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  /* Animation for the bouncing dots */
  .animate-bounce {
    animation: bounce 1s infinite;
  }

  @keyframes bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  /* Hide content until Alpine.js is loaded */
  [x-cloak] {
    display: none !important;
  }

  /* Smooth transition for the chat widget with opacity */
  #widget {
    transition: right 0.4s cubic-bezier(0.25, 1, 0.5, 1), 
                opacity 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  }

  /* Model selector styling */
  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    font-size: 0.75rem;
    max-width: 120px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  
  select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  /* Mobile responsive adjustments */
  @media (max-width: 767px) {
    .resize-handle {
      display: none;
    }
    
    select {
      min-width: 100px;
      max-width: 120px;
      font-size: 0.7rem;
      padding: 1px 6px;
    }
    
    /* Mobile chat box adjustments */
    #widget [x-ref="chatBox"] {
      width: 90vw !important;
      max-width: 360px !important;
      height: 70vh !important;
      max-height: 500px !important;
      min-height: 400px !important;
    }
    
    /* Adjust suggestions for mobile */
    .suggestions-container {
      padding-left: 12px;
      padding-right: 12px;
    }
    
    .suggestion-box {
      padding: 12px 16px;
      font-size: 0.875rem;
    }
  }

  /* Link styling in messages */
  .message-container a {
    color: #3D84F3;
    text-decoration: underline;
    transition: color 0.2s ease;
  }

  .message-container a:hover {
    color: #2D74E3;
  }
</style>

<script>
  // Add this at the bottom of the file, after all other script tags
  window.formatMessage = function(content) {
    if (!content) return '';
    return content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, function (_, text, url) {
      return '<a href="' + url + '" target="_blank" rel="noopener noreferrer" class="text-[#3D84F3] underline hover:text-[#2D74E3]">' + text + '</a>';
    });
  };
</script>
