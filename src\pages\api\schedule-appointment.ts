// src/pages/api/schedule-appointment.ts
import type { APIRoute } from 'astro';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  console.log('Appointment scheduling API called');
  
  try {
    if (!request.body) {
      return new Response(JSON.stringify({ error: 'Request body is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const appointmentData = await request.json();
    console.log('Appointment data received:', appointmentData);

    // Validate required fields for appointment scheduling
    const requiredFields = ['name', 'email', 'preferredTime', 'timezone'];
    const missingFields = requiredFields.filter(field => !appointmentData[field]);
    
    if (missingFields.length > 0) {
      return new Response(JSON.stringify({ 
        error: 'Missing required fields for appointment',
        missingFields 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Here you would integrate with Calendly API
    // For now, we'll simulate the scheduling process
    
    // TODO: Implement Calendly API integration
    /*
    const calendlyResponse = await fetch('https://api.calendly.com/scheduled_events', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.CALENDLY_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        event_type: 'https://calendly.com/stansolovyanchuk/30min',
        invitee: {
          name: appointmentData.name,
          email: appointmentData.email
        },
        start_time: appointmentData.preferredTime,
        timezone: appointmentData.timezone
      })
    });
    */

    // Simulate successful scheduling
    const appointmentId = `appt_${Date.now()}`;
    const scheduledTime = new Date(appointmentData.preferredTime).toISOString();

    console.log('Appointment scheduled successfully:', {
      id: appointmentId,
      name: appointmentData.name,
      email: appointmentData.email,
      scheduledTime,
      timezone: appointmentData.timezone
    });

    return new Response(JSON.stringify({ 
      success: true,
      appointmentId,
      scheduledTime,
      message: 'Appointment scheduled successfully',
      calendlyLink: 'https://calendly.com/stansolovyanchuk/30min' // Updated fallback link
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Appointment scheduling error:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Failed to schedule appointment',
      message: 'Please try using the calendar widget below or contact us directly'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
