---
// CalendlyIntegration.astro - Enhanced Calendly booking component with better UX
---

<div id="calendly-container" class="hidden">
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-4xl mx-auto max-h-[90vh] overflow-hidden">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-bold text-[#1E232E]">Schedule Your Discovery Call</h3>
      <button id="close-calendly" class="text-gray-500 hover:text-gray-700 transition-colors">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>
    
    <div class="text-center mb-4">``
      <p class="text-gray-600">Book a 15-minute discovery call to discuss how Clearbeam can help your business</p>
    </div>
    
    <!-- Calendly inline widget -->
    <div class="calendly-inline-widget" data-url="https://calendly.com/stansolovyanchuk/30min" style="min-width:320px;height:700px;"></div>
  </div>
</div>

<script>
  // Load Calendly widget script
  // SvelteKit will automatically apply the nonce to this script tag
  // if it's rendered on the server.
</script>
<script src="https://assets.calendly.com/assets/external/widget.js" async></script>
<script>

  // Function to show Calendly modal
  window.showCalendly = function() {
    const container = document.getElementById('calendly-container');
    if (container) {
      container.classList.remove('hidden');
      container.classList.add('fixed', 'inset-0', 'bg-black', 'bg-opacity-50', 'flex', 'items-center', 'justify-center', 'z-50', 'p-4');
      
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
      
      // Focus trap for accessibility
      const closeBtn = document.getElementById('close-calendly');
      if (closeBtn) {
        closeBtn.focus();
      }
    }
  };

  // Function to hide Calendly modal
  window.hideCalendly = function() {
    const container = document.getElementById('calendly-container');
    if (container) {
      container.classList.add('hidden');
      container.classList.remove('fixed', 'inset-0', 'bg-black', 'bg-opacity-50', 'flex', 'items-center', 'justify-center', 'z-50', 'p-4');
      
      // Restore body scroll
      document.body.style.overflow = '';
    }
  };

  // Initialize event listeners when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    const closeBtn = document.getElementById('close-calendly');
    if (closeBtn) {
      closeBtn.addEventListener('click', window.hideCalendly);
    }

    // Close on backdrop click
    const container = document.getElementById('calendly-container');
    if (container) {
      container.addEventListener('click', (e) => {
        if (e.target === container) {
          window.hideCalendly();
        }
      });
    }

    // Close on Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !container.classList.contains('hidden')) {
        window.hideCalendly();
      }
    });

    // Listen for Calendly events
    window.addEventListener('message', function(e) {
      if (e.data.event && e.data.event.indexOf('calendly') === 0) {
        console.log('Calendly event:', e.data.event);
        
        // Handle successful booking
        if (e.data.event === 'calendly.event_scheduled') {
          console.log('Appointment scheduled successfully');
          
          // Optional: Show success message or redirect
          setTimeout(() => {
            window.hideCalendly();
            
            // Show success notification
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50';
            successDiv.innerHTML = `
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                <span>Call scheduled successfully! We'll be in touch soon.</span>
              </div>
            `;
            document.body.appendChild(successDiv);
            
            // Remove success message after 5 seconds
            setTimeout(() => {
              if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
              }
            }, 5000);
          }, 1000);
        }
      }
    });
  });
</script>

<style>
  /* Enhanced modal styling */
  #calendly-container {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Calendly widget styling */
  .calendly-inline-widget {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    #calendly-container .max-w-4xl {
      max-width: 95vw;
      margin: 1rem;
    }
    
    .calendly-inline-widget {
      height: 600px !important;
      min-width: 280px !important;
    }
  }

  /* Loading state for Calendly widget */
  .calendly-inline-widget:empty::before {
    content: "Loading calendar...";
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;
    font-size: 1rem;
  }
</style>
