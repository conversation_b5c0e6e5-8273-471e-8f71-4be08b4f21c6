---
// ContactForm.astro - Enhanced contact form with validation and styling
---

<section class="py-16 bg-white" id="contact-form">
  <div class="max-w-4xl mx-auto px-4">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold text-[#1E232E] mb-4">Request Your Demo</h2>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        Get a personalized demo of how Clearbeam can transform your website into a 24/7 sales machine
      </p>
    </div>

    <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-8">
      <form id="demo-request-form" class="space-y-6">
        <!-- Name Fields -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="firstName" class="block text-sm font-medium text-[#1E232E] mb-2">
              First Name *
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3D84F3] focus:border-transparent transition-colors"
              placeholder="Enter your first name"
            />
            <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
          </div>
          <div>
            <label for="lastName" class="block text-sm font-medium text-[#1E232E] mb-2">
              Last Name *
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3D84F3] focus:border-transparent transition-colors"
              placeholder="Enter your last name"
            />
            <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
          </div>
        </div>

        <!-- Email -->
        <div>
          <label for="email" class="block text-sm font-medium text-[#1E232E] mb-2">
            Email Address *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3D84F3] focus:border-transparent transition-colors"
            placeholder="<EMAIL>"
          />
          <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
        </div>

        <!-- Phone -->
        <div>
          <label for="phone" class="block text-sm font-medium text-[#1E232E] mb-2">
            Phone Number *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3D84F3] focus:border-transparent transition-colors"
            placeholder="(*************"
          />
          <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
        </div>

        <!-- Company -->
        <div>
          <label for="company" class="block text-sm font-medium text-[#1E232E] mb-2">
            Company Name *
          </label>
          <input
            type="text"
            id="company"
            name="company"
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3D84F3] focus:border-transparent transition-colors"
            placeholder="Your Company Name"
          />
          <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
        </div>

        <!-- Website -->
        <div>
          <label for="website" class="block text-sm font-medium text-[#1E232E] mb-2">
            Website URL *
          </label>
          <input
            type="url"
            id="website"
            name="website"
            required
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3D84F3] focus:border-transparent transition-colors"
            placeholder="https://yourwebsite.com"
          />
          <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
        </div>

        <!-- Current Challenges -->
        <div>
          <label for="challenges" class="block text-sm font-medium text-[#1E232E] mb-2">
            What are your biggest challenges with customer support?
          </label>
          <textarea
            id="challenges"
            name="challenges"
            rows="4"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3D84F3] focus:border-transparent transition-colors resize-vertical"
            placeholder="Tell us about your current challenges with customer support, lead capture, or website engagement..."
          ></textarea>
          <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
        </div>

        <!-- Submit Button -->
        <div class="pt-4">
          <button
            type="submit"
            id="submit-btn"
            class="w-full bg-[#1E232E] text-white py-4 px-6 rounded-lg hover:bg-[#33384D] transition-colors duration-300 font-medium text-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="submit-text">Request Demo</span>
            <span class="loading-text hidden">Submitting...</span>
          </button>
        </div>

        <!-- Success Message -->
        <div id="success-message" class="hidden bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            <div>
              <h3 class="text-green-800 font-medium">Demo Request Submitted!</h3>
              <p class="text-green-700 text-sm mt-1">We'll be in touch within 24 hours to schedule your personalized demo.</p>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Calendly Integration Section -->
    <div class="mt-16 text-center">
      <h3 class="text-2xl font-bold text-[#1E232E] mb-4">Or Schedule a Call Directly</h3>
      <p class="text-gray-600 mb-8">Prefer to talk right away? Book a 15-minute discovery call with our team.</p>
      
      <!-- Calendly Inline Widget -->
      <div 
        class="calendly-inline-widget" 
        data-url="https://calendly.com/stansolovyanchuk/30min"
        style="min-width:320px;height:700px;"
      ></div>
    </div>
  </div>
</section>

<script>
  // Load Calendly widget script
  const calendlyScript = document.createElement('script');
  calendlyScript.src = 'https://assets.calendly.com/assets/external/widget.js';
  calendlyScript.async = true;
  document.head.appendChild(calendlyScript);

  // Form validation and submission
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('demo-request-form');
    const submitBtn = document.getElementById('submit-btn');
    const successMessage = document.getElementById('success-message');

    // Real-time validation
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      input.addEventListener('blur', validateField);
      input.addEventListener('input', clearError);
    });

    function validateField(e) {
      const field = e.target;
      const errorDiv = field.parentNode.querySelector('.error-message');
      
      if (!field.value.trim() && field.required) {
        showError(field, errorDiv, 'This field is required');
        return false;
      }

      // Email validation
      if (field.type === 'email' && field.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(field.value)) {
          showError(field, errorDiv, 'Please enter a valid email address');
          return false;
        }
      }

      // URL validation
      if (field.type === 'url' && field.value) {
        try {
          new URL(field.value);
        } catch {
          showError(field, errorDiv, 'Please enter a valid URL (e.g., https://yoursite.com)');
          return false;
        }
      }

      // Phone validation
      if (field.type === 'tel' && field.value) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        const cleanPhone = field.value.replace(/\D/g, '');
        if (cleanPhone.length < 10) {
          showError(field, errorDiv, 'Please enter a valid phone number');
          return false;
        }
      }

      hideError(field, errorDiv);
      return true;
    }

    function showError(field, errorDiv, message) {
      field.classList.add('border-red-500');
      field.classList.remove('border-gray-300');
      errorDiv.textContent = message;
      errorDiv.classList.remove('hidden');
    }

    function hideError(field, errorDiv) {
      field.classList.remove('border-red-500');
      field.classList.add('border-gray-300');
      errorDiv.classList.add('hidden');
    }

    function clearError(e) {
      const field = e.target;
      const errorDiv = field.parentNode.querySelector('.error-message');
      if (!errorDiv.classList.contains('hidden')) {
        hideError(field, errorDiv);
      }
    }

    // Form submission
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      // Validate all fields
      let isValid = true;
      inputs.forEach(input => {
        if (!validateField({ target: input })) {
          isValid = false;
        }
      });

      if (!isValid) {
        // Scroll to first error
        const firstError = form.querySelector('.border-red-500');
        if (firstError) {
          firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return;
      }

      // Show loading state
      submitBtn.disabled = true;
      submitBtn.querySelector('.submit-text').classList.add('hidden');
      submitBtn.querySelector('.loading-text').classList.remove('hidden');

      try {
        // Collect form data
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Submit to API (you can implement this endpoint)
        const response = await fetch('/api/submit-demo-request', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (response.ok) {
          // Show success message
          form.classList.add('hidden');
          successMessage.classList.remove('hidden');
          
          // Scroll to success message
          successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
          
          // Optional: Trigger analytics event
          if (typeof gtag !== 'undefined') {
            gtag('event', 'form_submit', {
              event_category: 'engagement',
              event_label: 'demo_request'
            });
          }
        } else {
          throw new Error('Submission failed');
        }
      } catch (error) {
        console.error('Form submission error:', error);
        alert('There was an error submitting your request. Please try again or contact us directly.');
      } finally {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.querySelector('.submit-text').classList.remove('hidden');
        submitBtn.querySelector('.loading-text').classList.add('hidden');
      }
    });

    // Make form accessible to chat widget
    window.contactForm = {
      scrollToForm: () => {
        document.getElementById('contact-form').scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      },
      fillField: (fieldName, value) => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (field) {
          field.value = value;
          field.dispatchEvent(new Event('input', { bubbles: true }));
        }
      },
      getFieldValue: (fieldName) => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        return field ? field.value : null;
      },
      validateForm: () => {
        let isValid = true;
        inputs.forEach(input => {
          if (!validateField({ target: input })) {
            isValid = false;
          }
        });
        return isValid;
      }
    };
  });
</script>

<style>
  /* Enhanced form styling */
  .calendly-inline-widget {
    border-radius: 8px;
    overflow: hidden;
  }

  /* Loading animation */
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .loading-text {
    animation: pulse 1.5s ease-in-out infinite;
  }

  /* Focus states */
  input:focus, select:focus, textarea:focus {
    box-shadow: 0 0 0 3px rgba(61, 132, 243, 0.1);
  }

  /* Custom select arrow */
  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .calendly-inline-widget {
      height: 600px !important;
    }
  }
</style>
