import { v4 as uuidv4 } from 'uuid';

export async function handle({ event, resolve }) {
  const nonce = Buffer.from(uuidv4()).toString('base64');
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'nonce-" + nonce + "'",
    "style-src 'self' 'unsafe-inline' https://assets.calendly.com",
    "frame-src https://calendly.com",
    "img-src 'self' data:",
    "connect-src 'self'",
    "object-src 'none'",
    "base-uri 'self'"
  ].join('; ');

  const response = await resolve(event, {
    transformPageChunk: ({ html }) => html.replace('%sveltekit.nonce%', nonce)
  });

  response.headers.set('Content-Security-Policy', csp);

  return response;
}