<nav
  x-data="{ mobileMenuIsOpen: false }"
  x-on:click.away="mobileMenuIsOpen = false"
  class="transition-all duration-300 ease flex items-center justify-between px-6 py-4 fixed w-[100vw] z-1000 backdrop-blur-lg"
  aria-label="clearbeam menu"
>
  <!-- <PERSON> -->
  <a
    href="/"
    class="text-2xl font-bold text-on-surface-strong dark:text-on-surface-dark-strong"
  >
    <svg
      class="w-10 h-10"
      viewBox="0 0 303 268"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M134.105 198.829C169.921 198.829 198.955 169.795 198.955 133.979C198.955 98.164 169.921 69.1299 134.105 69.1299C98.29 69.1299 69.2559 98.164 69.2559 133.979C69.2559 169.795 98.29 198.829 134.105 198.829Z"
        fill="#D9D9D9"></path>
      <path
        d="M255.572 185.366C244.434 212.554 225.54 234.425 204.006 247.666C182.472 260.906 157.62 267.758 132.343 267.424C107.066 267.09 82.4036 259.584 61.2265 245.779C40.0494 231.975 23.2286 212.439 12.7219 189.447C2.21528 166.454 -1.54505 140.951 1.87862 115.905C5.30228 90.8587 15.7691 67.2999 32.0609 47.9707C48.3526 28.6414 69.7991 14.3369 93.9041 6.72187C118.009 -0.89307 152.059 -2.33159 176.627 7.8231L159.051 39.6339C141.18 34.9087 122.335 35.3563 104.709 40.9246C87.0826 46.4929 71.4002 56.9528 59.4871 71.087C47.5741 85.2212 39.9204 102.448 37.4169 120.762C34.9134 139.077 37.6631 157.726 45.3459 174.539C53.0287 191.352 65.3286 205.637 80.814 215.731C96.2994 225.826 114.333 231.314 132.816 231.559C151.3 231.803 169.472 226.792 185.219 217.11C200.966 207.428 213.639 193.473 221.763 176.869L255.572 185.366Z"
        fill="#1E232E"></path>
      <path
        d="M206.254 4.32906L158.889 82.7353C157.44 85.134 158.21 88.2533 160.609 89.7024L160.609 89.7024C163.007 91.1515 166.127 90.3817 167.576 87.9829L214.941 9.57668C216.39 7.17792 215.62 4.05861 213.221 2.60951C210.823 1.16042 207.703 1.9303 206.254 4.32906Z"
        fill="#3D84F3"></path>
      <path
        d="M271.278 71.0915L190.65 104.889C188.065 105.972 186.848 108.946 187.932 111.53L187.932 111.53C189.015 114.115 191.989 115.332 194.573 114.248L275.201 80.4512C277.786 79.3678 279.003 76.3943 277.919 73.8096C276.836 71.225 273.863 70.0081 271.278 71.0915Z"
        fill="#3D84F3"></path>
      <path
        d="M298.005 162.431L189.145 139.75C186.401 139.179 183.714 140.939 183.142 143.683L183.142 143.683C182.571 146.427 184.331 149.114 187.075 149.686L295.935 172.367C298.678 172.938 301.366 171.177 301.937 168.434C302.509 165.69 300.748 163.003 298.005 162.431Z"
        fill="#3D84F3"></path>
      <path
        d="M134.105 198.829C169.921 198.829 198.955 169.795 198.955 133.979C198.955 98.164 169.921 69.1299 134.105 69.1299C98.29 69.1299 69.2559 98.164 69.2559 133.979C69.2559 169.795 98.29 198.829 134.105 198.829Z"
        fill="#3D84F3"></path>
    </svg>
  </a>
  <!-- Desktop Menu -->
  <ul class="items-center gap-4 sm:flex hidden">
    <li>
      <a
        href="/#pricing"
        class="font-medium text-on-surface bg-[#F8F8FA] underline-offset-2 hover:text-primary focus:outline-hidden focus:underline dark:text-on-surface-dark dark:hover:text-primary-dark"
        >Pricing</a>
    </li>
    <!-- CTA Button -->
    <li>
      <a
        href="/contact"
        class="rounded-lg bg-[#1E232E] border border-[#1E232E] duration-300 px-2 py-2 text-center text-sm font-semibold tracking-wide text-[#FFFFFC] hover:bg-[#F8F8FA] hover:text-[#1E232E] ease focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:opacity-100 active:outline-offset-0 dark:bg-primary-dark dark:border-primary-dark dark:text-on-primary-dark dark:focus-visible:outline-primary-dark"
        >Request a Demo</a>
    </li>
  </ul>
  <!-- Mobile Menu Button -->
  <button
    x-on:click="mobileMenuIsOpen = !mobileMenuIsOpen"
    x-bind:aria-expanded="mobileMenuIsOpen"
    x-bind:class="mobileMenuIsOpen ? 'fixed top-6 right-6 z-20' : null"
    type="button"
    class="flex text-on-surface dark:text-on-surface-dark sm:hidden"
    aria-label="mobile menu"
    aria-controls="mobileMenu"
  >
    <svg
      x-cloak
      x-show="!mobileMenuIsOpen"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      aria-hidden="true"
      viewBox="0 0 24 24"
      stroke-width="2"
      stroke="currentColor"
      class="size-6 z-22"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
    </svg>
    <svg
      x-cloak
      x-show="mobileMenuIsOpen"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      aria-hidden="true"
      viewBox="0 0 24 24"
      stroke-width="2"
      stroke="currentColor"
      class="size-6 z-22"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M6 18 18 6M6 6l12 12"></path>
    </svg>
  </button>
  <!-- Mobile Menu -->
  <ul
    x-cloak
    x-show="mobileMenuIsOpen"
    x-transition:enter="transition motion-reduce:transition-none ease-out duration-300 z-20"
    x-transition:enter-start="-translate-y-full"
    x-transition:enter-end="translate-y-0"
    x-transition:leave="transition motion-reduce:transition-none ease-out duration-300"
    x-transition:leave-start="translate-y-0"
    x-transition:leave-end="-translate-y-full"
    id="mobileMenu"
    class="fixed max-h-svh overflow-y-auto inset-x-0 top-0 z-10 flex flex-col divide-y divide-outline rounded-b-radius bg-surface-alt px-6 pb-6 pt-0 dark:divide-outline-dark dark:bg-surface-dark-alt sm:hidden text-[#1E232E] bg-white rounded-lg"
    style="box-shadow: 0px 3px 3px #00000020;"
  >
    <li
      class="mt-4 w-full border-none"
      x-on:click="mobileMenuIsOpen = !mobileMenuIsOpen; window.location.href='/';"
    >
      <span
        class="text-xl mb-4 w-full rounded-radius px-4 py-2 block text-center font-semibold tracking-wide text-[#1E232E] duration-100 ease rounded-lg focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:opacity-100 active:outline-offset-0 dark:bg-primary-dark dark:border-primary-dark dark:text-on-primary-dark dark:focus-visible:outline-primary-dark"
        >Clearbeam</span>
    </li>
    <li class="mt-4 w-full border-none">
      <button
        x-on:click="if (true) {mobileMenuIsOpen = !mobileMenuIsOpen; window.location.href='/#pricing';}"
        class="w-full rounded-radius px-4 py-3 block text-center font-medium tracking-wide text-[#1E232E]   bg-white hover:bg-[#DBDBE5] duration-100 ease rounded-lg focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary active:opacity-100 active:outline-offset-0 dark:bg-primary-dark dark:border-primary-dark dark:text-on-primary-dark dark:focus-visible:outline-primary-dark"
        >Pricing</button>
    </li>
    <!-- CTA Button -->
    <li class="mt-4 w-full border-none">
      <a
        href="/contact"
        class="rounded-radius bg-[#1E232E] px-4 py-3 block text-center font-medium tracking-wide text-[#FFFFFC] hover:bg-[#33384D] duration-100 ease focus-visible:outline-2 rounded-lg focus-visible:outline-offset-2 focus-visible:outline-primary active:opacity-100 active:outline-offset-0 dark:bg-primary-dark dark:border-primary-dark dark:text-on-primary-dark dark:focus-visible:outline-primary-dark"
        >Request a Demo</a>
    </li>
  </ul>
</nav>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.getElementById('navbar');
    const isContactPage = window.location.pathname.includes('/contact');
    
    if (isContactPage) {
      // On contact page, make navbar white with shadow from initial load
      if (navbar) {
        navbar.classList.add('bg-white', 'shadow-md');
        navbar.classList.remove('bg-transparent', 'transition-all', 'duration-300');
      }
    } else {
      // On other pages, keep the current transition/animation
      window.addEventListener('scroll', function() {
        if (navbar) {
          if (window.scrollY > 10) {
            navbar.classList.add('bg-white', 'shadow-md');
            navbar.classList.remove('bg-transparent');
          } else {
            navbar.classList.add('bg-transparent');
            navbar.classList.remove('bg-white', 'shadow-md');
          }
        }
      });
      
      // Initial check
      if (window.scrollY > 10 && navbar) {
        navbar.classList.add('bg-white', 'shadow-md');
        navbar.classList.remove('bg-transparent');
      }
    }
  });
</script>

