---
import "../styles/global.css";

interface Props {
  title?: string;
}

const { title = "Clearbeam" } = Astro.props;
---

<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Clearbeam - AI-powered customer support for your business" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Kumbh+Sans:wght@100..900&display=swap"
      rel="stylesheet"
    />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>
  </head>
  <body class="bg-[#F7F8F9] overflow-x-hidden">
    <slot />
  </body>
  <script src="https://clearbeam-widget.vercel.app/clearbeam-widget.iife.js" data-auto-init></script>
</html>

<style>
  html,
  body {
    margin: 0;
    width: 100%;
    height: 100%;
    font-family: Kumbh Sans, Inter, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }
  /* Scrollbar styling for WebKit (Chrome, Safari) */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: #20232D;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #20232D;
    border-radius: 5px;
    border: 2px solid #f0f0f000;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #20232D;
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: #20232D #FFFFFF00;
  }
</style>

<script>
  import Alpine from 'alpinejs';
  //@ts-ignore
  import collapse from '@alpinejs/collapse';
  //@ts-ignore
  import focus from '@alpinejs/focus';
  
  // Register Alpine plugins
  Alpine.plugin(collapse);
  Alpine.plugin(focus);
</script>