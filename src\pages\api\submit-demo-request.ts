// src/pages/api/submit-demo-request.ts
import type { APIRoute } from 'astro';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  console.log('Demo request submission API called');
  
  try {
    if (!request.body) {
      return new Response(JSON.stringify({ error: 'Request body is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const formData = await request.json();
    console.log('Form data received:', formData);

    // Validate required fields
    const requiredFields = [
      'firstName', 'lastName', 'email', 'phone', 'company', 
      'website', 'businessType', 'monthlyTraffic', 'challenges', 
      'goals', 'budget', 'timeline'
    ];

    const missingFields = requiredFields.filter(field => !formData[field]);
    
    if (missingFields.length > 0) {
      return new Response(JSON.stringify({ 
        error: 'Missing required fields',
        missingFields 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      return new Response(JSON.stringify({ 
        error: 'Invalid email format' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate website URL
    try {
      new URL(formData.website);
    } catch {
      return new Response(JSON.stringify({ 
        error: 'Invalid website URL format' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Here you would typically:
    // 1. Save to database (Supabase, etc.)
    // 2. Send notification email
    // 3. Add to CRM
    // 4. Trigger automation workflows

    // For now, we'll just log the data and return success
    console.log('Demo request processed successfully:', {
      name: `${formData.firstName} ${formData.lastName}`,
      email: formData.email,
      company: formData.company,
      website: formData.website,
      businessType: formData.businessType,
      monthlyTraffic: formData.monthlyTraffic,
      budget: formData.budget,
      timeline: formData.timeline,
      timestamp: new Date().toISOString()
    });

    // TODO: Implement actual data storage and notifications
    // Example integrations you might add:
    /*
    // Save to Supabase
    const { data, error } = await supabase
      .from('demo_requests')
      .insert([formData]);

    // Send notification email
    await sendNotificationEmail(formData);

    // Add to CRM (HubSpot, etc.)
    await addToCRM(formData);
    */

    return new Response(JSON.stringify({ 
      success: true,
      message: 'Demo request submitted successfully',
      id: `demo_${Date.now()}` // Temporary ID
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Demo request submission error:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      message: 'Failed to process demo request'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};