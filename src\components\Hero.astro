---
import ChatWidgetExample from "./ChatWidgetExample.astro";
---

<section class="px-2 py-20 lg:py-32 bg-inherit md:px-0 min-h-screen">
  <div class="container items-center max-w-6xl px-8 mx-auto xl:px-5">
    <div class="flex flex-wrap items-center sm:-mx-3">
      <div class="w-full md:w-1/2 md:px-3">
        <div
          class="w-full pb-6 space-y-6 sm:max-w-md lg:max-w-lg md:space-y-4 lg:space-y-8 xl:space-y-9 sm:pr-5 lg:pr-0 md:pb-0"
        >
          <h1
            class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-4xl lg:text-5xl xl:text-6xl"
          >
            <span class="block xl:inline"
              >Turn website visitors into loyal customers <br> instantly.</span
            >
          </h1>
          <p
            class="mx-auto text-base text-gray-500 sm:max-w-md lg:text-xl md:max-w-3xl"
          >
          We build AI-powered chat widgets tailored to your business, helping you engage and assist your customers 24/7 — with no extra overhead.
          </p>
          <div
            class="relative flex flex-col sm:flex-row sm:space-x-4 lg:gap-[4%]"
          >
            <button
              onclick="window.location.href='/contact'"
              class="group not-prose py-3 inline-flex items-center gap-1 justify-center lg:w-[47%] mb-3 rounded-md font-bold bg-[#1E232E] text-[#F7F8F9] hover:text-[#] hover:dark:text-white border-[#1E232E] dark:border-zinc-700 hover:bg-zinc-200 hover:dark:bg-zinc-800 transition-colors duration-300 ease-in-out"
            >
              <p>Let's get started!</p><svg
                viewBox="0 0 24 24"
                class="size-5 stroke-[3px] fill-none stroke-current opacity-100 group-hover:opacity-100 transition-opacity duration-100 ease-in-out"
              >
                <line
                  x1="5"
                  y1="12"
                  x2="19"
                  y2="12"
                  class="scale-x-0 translate-x-[10px] group-hover:translate-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-in-out"
                ></line>
                <polyline
                  points="12 5 19 12 12 19"
                  class="-translate-x-2 group-hover:translate-x-0 transition-transform duration-300 ease-in-out"
                ></polyline>
              </svg>
            </button>
            <button
              onclick="window.location.href='/#pricing'"
              class="mb-3 lg:w-[45%] py-2 rounded-md font-bold bg-inherit border text-current hover:text-black hover:dark:text-white border-[#1E232E] dark:border-zinc-700 hover:bg-[#1E232E] hover:dark:bg-[#1E232E] transition-colors duration-200 ease-in-out"
            >
              <p>Check out the prices</p>
            </button>
          </div>
        </div>
      </div>
      <div class="w-full md:w-1/2">
        <div
          class="w-full h-auto overflow-visible rounded-md sm:rounded-xl flex justify-center items-center p-2 md:p-10 mt-8 md:mt-0"
          data-rounded="rounded-xl"
          data-rounded-max="rounded-full"
        >
          <ChatWidgetExample />
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  // Hero section - demo now runs automatically via intersection observer
  document.addEventListener('DOMContentLoaded', () => {
    console.log('Hero section loaded - chat widget demo will start automatically when visible');
  });
</script>