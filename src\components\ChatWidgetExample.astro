---
// Enhanced ChatWidgetExample.astro with smooth animations and suggestions
const initialMessage = {
  role: 'assistant',
  content: 'Hey there! 👋 How can I assist you today?'
};
---

<div class='w-full max-w-md mx-auto transform transition-all duration-700'>
  <div class="w-full h-[650px] md:h-[600px] rounded-2xl flex flex-col overflow-hidden border border-[#DCDCDC] bg-white shadow-widget" style="animation: forwards arise 2s ease .3s;">
    <div class="flex items-center justify-between px-4 py-4 border-b border-[#DCDCDC]">
      <span class="text-sm font-semibold text-[#1E232E] mx-auto">CB Assistant</span>
      <button class="text-[#1E232E] hover:text-gray-600 transition-colors" aria-label="Close chat">
        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0.988873 6.95887C0.713231 7.23906 0.697766 7.75668 0.99888 8.05234C1.29908 8.35254 1.81216 8.34345 2.09326 8.06235L4.87152 5.28318L7.64614 8.0578C7.93725 8.35345 8.43941 8.3489 8.73506 8.04779C8.87837 7.90336 8.95954 7.70863 8.96124 7.50518C8.96294 7.30173 8.88504 7.10567 8.74416 6.95887L5.97227 4.18425L8.74689 1.40417C8.88776 1.25737 8.96567 1.06131 8.96397 0.857855C8.96227 0.654401 8.8811 0.459673 8.73779 0.315245C8.59333 0.171666 8.3984 0.090326 8.19473 0.0886259C7.99106 0.0869258 7.7948 0.165001 7.64796 0.306148L4.87152 3.07986L2.09326 0.29978C1.81125 0.0241372 1.29454 0.00867205 0.99888 0.308877C0.703224 0.609991 0.713231 1.12307 0.989783 1.40326L3.76895 4.18334L0.988873 6.95796V6.95887Z" fill="currentColor" />
        </svg>
      </button>
    </div>

    <div id="example-messages-container" class="flex-1 p-4 overflow-y-auto messages-container scrollbar-thin scrollbar-track-transparent relative">
      <div id="example-messages-list" class="flex flex-col space-y-3 min-h-full">
        <div class="message-container self-start max-w-[70%] opacity-0 transform translate-y-4">
          <div class="p-3 rounded-lg break-words bg-[#1E232E] text-[#F8F8FA]">
            {initialMessage.content}
          </div>
        </div>
      </div>
      
      <!-- Fixed position typing indicator -->
      <div id="example-typing-indicator" class="typing-indicator bg-gray-200 p-3 rounded-lg flex space-x-2 items-center opacity-0 max-w-[70%] fixed-typing-indicator">
        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
      </div>
    </div>

    <!-- Chat Suggestions - positioned absolutely to avoid space issues -->
    <div id="example-suggestions" class="absolute bottom-[60px] left-4 right-4 space-y-2 suggestions-container opacity-0 z-10">
      <button class="w-full text-left p-3 bg-[#E6F3FF] hover:bg-[#D1E9FF] text-[#2D3748] rounded-lg border border-[#B8E0FF] transition-colors duration-200 text-sm font-medium suggestion-box">
        💰 Explain pricing options
      </button>
      <button class="w-full text-left p-3 bg-[#E6F3FF] hover:bg-[#D1E9FF] text-[#2D3748] rounded-lg border border-[#B8E0FF] transition-colors duration-200 text-sm font-medium suggestion-box">
        ⚙️ How does it work?
      </button>
    </div>

    <div class="flex items-center px-2 py-2 border-t border-[#DCDCDC] bg-white">
      <div class="flex-1 relative flex items-center">
        <div class="typing-cursor-container flex-1 pl-3 pr-10 py-2 text-sm rounded-md bg-[#F8F8FA] border border-[#DCDCDC] text-[#1E232E] overflow-hidden">
          <div id="typing-text-container" class="flex items-center whitespace-nowrap overflow-hidden h-full">
            <span id="typing-text" class="typing-text"></span>
            <span id="typing-cursor" class="typing-cursor">|</span>
          </div>
        </div>
        <button class="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-[#1E232E] hover:text-[#3D84F3] transition-colors z-10" aria-label="Send message">
          <img src="/src/assets/send_chat.svg" alt="Send" width="22" class="transition-transform duration-300 ease-in-out hover:scale-110" />
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  import { gsap } from 'gsap';
  import { ScrollToPlugin } from 'gsap/ScrollToPlugin';

  gsap.registerPlugin(ScrollToPlugin);

  document.addEventListener('DOMContentLoaded', () => {
    const messagesContainer = document.getElementById('example-messages-container');
    const messagesList = document.getElementById('example-messages-list');
    const typingIndicator = document.getElementById('example-typing-indicator');
    const suggestionsContainer = document.getElementById('example-suggestions');
    const typingText = document.getElementById('typing-text');
    const typingCursor = document.getElementById('typing-cursor');
    const widgetContainer = document.querySelector('.max-w-md');
    const inputContainer = document.querySelector('.typing-cursor-container'); 
    const textContentContainer = document.getElementById('typing-text-container'); 

    // Enhanced widget shadow animation
    gsap.from(widgetContainer, {
      boxShadow: "0px 0px 0px rgba(95, 133, 235, 0)",
      to: { boxShadow: "0px 10px 30px rgba(95, 133, 235, 0.3)" },
      duration: 1.5,
      ease: "power2.out"
    });
    
    const mockConversation = [
      { role: 'user', content: "What pricing options do you offer?", delay: 3000, typingDuration: 1200 },
      { role: 'assistant', content: 'We have several plans starting at $99/month. Our Starter plan includes AI chat, basic customization, and up to 500 conversations. The Professional plan at $249/month adds advanced features and analytics. Would you like me to explain the features of each plan?', delay: 1800, typingIndicatorDuration: 2000 },
      { role: 'user', content: 'Yes, please tell me more about the Professional plan features.', delay: 2500, typingDuration: 1800 },
      { role: 'assistant', content: 'The Professional plan includes everything in Starter plus: advanced customization options, up to 2,000 conversations per month, priority support, and a comprehensive analytics dashboard to track performance. Perfect for growing businesses that need more robust features. Would you like to schedule a demo?', delay: 1800, typingIndicatorDuration: 2200 }
    ];
    
    if (typingCursor) {
      gsap.to(typingCursor, {
        opacity: 0,
        duration: 0.5,
        repeat: -1,
        yoyo: true,
        ease: "power1.inOut"
      });
    }

    function scrollTextInputToEnd() {
      if (textContentContainer) {
        requestAnimationFrame(() => {
          if (textContentContainer && textContentContainer.scrollWidth > textContentContainer.clientWidth) {
            textContentContainer.scrollLeft = textContentContainer.scrollWidth - textContentContainer.clientWidth;
          } else if (textContentContainer) {
            textContentContainer.scrollLeft = 0; 
          }
        });
      }
    }
    
    if (inputContainer && typingText && textContentContainer) { 
      inputContainer.addEventListener('click', () => {
        document.addEventListener('keydown', (event) => {
          if (!typingText) return; 

          if (event.key === 'Backspace') {
            if (typingText.textContent) {
              typingText.textContent = typingText.textContent.slice(0, -1);
            }
          } else if (event.key.length === 1 && !event.ctrlKey && !event.metaKey && !event.altKey) { 
            typingText.textContent = (typingText.textContent || '') + event.key;
          }
          scrollTextInputToEnd(); 
        });
      });
    }
    
    function smoothScrollToBottom() { 
      if (!messagesContainer) return;
      gsap.to(messagesContainer, {
        scrollTo: { y: "max", offsetY: 0 },
        duration: 0.7,
        ease: "power2.out"
      });
    }

    function showTypingIndicator() { 
      if (typingIndicator) {
        // Position the typing indicator at the bottom of the messages
        const messagesRect = messagesList.getBoundingClientRect();
        const containerRect = messagesContainer.getBoundingClientRect();
        const scrollTop = messagesContainer.scrollTop;
        const messagesHeight = messagesList.scrollHeight;
        
        // Position it right after the last message
        typingIndicator.style.position = 'absolute';
        typingIndicator.style.bottom = 'auto';
        typingIndicator.style.top = `${messagesHeight + 16}px`; // 16px spacing
        typingIndicator.style.left = '16px';
        
        gsap.to(typingIndicator, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.4,
          ease: "back.out(1.2)"
        });
        
        // Scroll to show the typing indicator
        setTimeout(() => {
          smoothScrollToBottom();
        }, 100);
      }
    }

    function hideTypingIndicator(callback) { 
      if (typingIndicator) {
        gsap.to(typingIndicator, {
          opacity: 0,
          y: 20,
          scale: 0.8,
          duration: 0.3,
          ease: "power2.in",
          onComplete: callback
        });
      } else if (callback) {
        callback();
      }
    }

    function showSuggestions() {
      if (suggestionsContainer) {
        const suggestionBoxes = suggestionsContainer.querySelectorAll('.suggestion-box');
        gsap.set(suggestionBoxes, { opacity: 0, y: 20, scale: 0.95 });
        gsap.to(suggestionsContainer, { opacity: 1, duration: 0.3 });
        gsap.to(suggestionBoxes, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.4,
          ease: "back.out(1.2)",
          stagger: 0.1
        });
      }
    }

    function hideSuggestions() {
      if (suggestionsContainer) {
        gsap.to(suggestionsContainer, {
          opacity: 0,
          duration: 0.3,
          ease: "power2.in"
        });
      }
    }

    function createMessageElement(message) { 
      const messageDiv = document.createElement('div');
      messageDiv.className = `message-container ${message.role === 'assistant' ? 'self-start' : 'self-end'} max-w-[70%] opacity-0 transform translate-y-4`;
      const contentDiv = document.createElement('div');
      contentDiv.className = `p-3 rounded-lg break-words ${message.role === 'assistant' ? 'bg-[#1E232E] text-[#F8F8FA]' : 'bg-[#F8F8FA] border border-[#DCDCDC]'}`;
      contentDiv.textContent = message.content;
      messageDiv.appendChild(contentDiv);
      return messageDiv;
    }

    function animateMessage(element) { 
      gsap.to(element, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.5,
        ease: "power2.out",
        onComplete: () => {
          smoothScrollToBottom();
          // Update typing indicator position after new message
          if (typingIndicator && !typingIndicator.classList.contains('opacity-0')) {
            const messagesHeight = messagesList.scrollHeight;
            typingIndicator.style.top = `${messagesHeight + 16}px`;
          }
        }
      });
    }
    
    function simulateTyping(text, duration, callback) { 
      if (!typingText || !typingCursor) return;
      
      typingText.textContent = '';
      gsap.set(typingCursor, { opacity: 1 });
      
      const typingSpeed = duration / text.length;
      let currentText = '';
      let currentIndex = 0;
      
      const typeInterval = setInterval(() => {
        if (currentIndex < text.length) {
          currentText += text[currentIndex];
          typingText.textContent = currentText;
          currentIndex++;
          scrollTextInputToEnd(); 
        } else {
          clearInterval(typeInterval);
          setTimeout(() => {
            if (typingText) {
              gsap.to(typingText, {
                duration: 0.3,
                opacity: 0,
                onComplete: () => {
                  if (typingText) {
                    typingText.textContent = '';
                    gsap.set(typingText, { opacity: 1 });
                  }
                  scrollTextInputToEnd(); 
                  if (callback) callback();
                }
              });
            }
          }, 500);
        }
      }, typingSpeed);
    }
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const firstMessage = document.querySelector('.message-container');
          if (firstMessage) {
            setTimeout(() => {
              animateMessage(firstMessage);
              // Show suggestions after initial message
              setTimeout(() => {
                showSuggestions();
              }, 800);
            }, 500);
          }
          
          let currentDelay = 1;
          
          mockConversation.forEach((message, index) => {
            currentDelay += message.delay;
            
            if (message.role === 'user') {
              setTimeout(() => {
                // Hide suggestions when user starts typing
                if (index === 0) {
                  hideSuggestions();
                }
                simulateTyping(message.content, message.typingDuration || 1500, () => {
                  const messageElement = createMessageElement(message);
                  if (messagesList) messagesList.appendChild(messageElement);
                  animateMessage(messageElement);
                });
              }, currentDelay);
              currentDelay += (message.typingDuration || 1500) + 500;
            } else {
              setTimeout(() => {
                showTypingIndicator();
                setTimeout(() => {
                  hideTypingIndicator(() => {
                    const messageElement = createMessageElement(message);
                    if (messagesList) messagesList.appendChild(messageElement);
                    animateMessage(messageElement);
                  });
                }, message.typingIndicatorDuration || 1500);
              }, currentDelay);
              currentDelay += (message.typingIndicatorDuration || 1500) + 500;
            }
          });
          observer.disconnect();
        }
      });
    }, { threshold: 0.5 });

    if (messagesContainer) {
      observer.observe(messagesContainer);
    }
  });
</script>

<style>
  .messages-container {
    display: flex;
    flex-direction: column;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .message-container {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  .fixed-typing-indicator {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    pointer-events: none;
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    position: absolute;
    z-index: 5;
  }

  .suggestions-container {
    opacity: 0;
    will-change: transform, opacity;
    background: linear-gradient(to top, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
    backdrop-filter: blur(2px);
    padding: 8px;
    border-radius: 8px;
  }

  .suggestion-box {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  .typing-cursor-container {
    display: flex;
    align-items: center;
  }

  #typing-text-container {
    flex-grow: 1;
    min-width: 0;
  }

  .typing-text {
    display: inline-block;
    white-space: nowrap;
  }

  .typing-cursor {
    display: inline-block;
    margin-left: 2px;
  }

  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }

  .scrollbar-thin::-webkit-scrollbar { width: 6px; }
  .scrollbar-thin::-webkit-scrollbar-track { background: transparent; }
  .scrollbar-thin::-webkit-scrollbar-thumb { background-color: #CBD5E1; border-radius: 3px; }
  .scrollbar-thin::-webkit-scrollbar-thumb:hover { background-color: #94A3B8; }

  @keyframes arise {
    from { box-shadow: 0px 3px 4px #5F85EB00; } 
    to { box-shadow: 0px 3px 20px #5F85EBa3; }
  }

  .chat-example-container { 
    position: relative; 
    z-index: 1; 
    margin: 10px; 
  }

  @media (max-width: 767px) {
    .chat-example-container { 
      margin: 20px; 
      max-width: calc(100% - 40px); 
    }
    
    .suggestion-box {
      padding: 12px 16px;
      font-size: 0.875rem;
    }
  }
</style>