---
// Scripted Demo Chat Widget - Uses actual ChatWidget interface with Alpine.js demo
---

<div
  x-data="chatDemo()"
  x-intersect="startDemo()"
  class="w-full max-w-md mx-auto transform transition-all duration-700"
>
  <div class="w-full h-[650px] md:h-[600px] rounded-2xl flex flex-col overflow-hidden border border-[#DCDCDC] bg-white shadow-xl" style="box-shadow: 0px 10px 30px rgba(95, 133, 235, 0.3);">
    <!-- Header -->
    <div class="flex items-center justify-between px-4 py-4 border-b border-[#DCDCDC]">
      <span class="text-sm font-semibold text-[#1E232E] mx-auto">CB Assistant</span>
      <button class="text-[#1E232E] hover:text-gray-600 transition-colors" aria-label="Close chat">
        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M0.988873 6.95887C0.713231 7.23906 0.697766 7.75668 0.99888 8.05234C1.29908 8.35254 1.81216 8.34345 2.09326 8.06235L4.87152 5.28318L7.64614 8.0578C7.93725 8.35345 8.43941 8.3489 8.73506 8.04779C8.87837 7.90336 8.95954 7.70863 8.96124 7.50518C8.96294 7.30173 8.88504 7.10567 8.74416 6.95887L5.97227 4.18425L8.74689 1.40417C8.88776 1.25737 8.96567 1.06131 8.96397 0.857855C8.96227 0.654401 8.8811 0.459673 8.73779 0.315245C8.59333 0.171666 8.3984 0.090326 8.19473 0.0886259C7.99106 0.0869258 7.7948 0.165001 7.64796 0.306148L4.87152 3.07986L2.09326 0.29978C1.81125 0.0241372 1.29454 0.00867205 0.99888 0.308877C0.703224 0.609991 0.713231 1.12307 0.989783 1.40326L3.76895 4.18334L0.988873 6.95796V6.95887Z" fill="currentColor" />
        </svg>
      </button>

    <!-- Messages Area -->
    <div
      x-ref="messages"
      class="flex-1 p-4 overflow-y-auto text-sm text-[#1E232E] scrollbar-hide relative"
    >
      <div class="flex flex-col space-y-3">
        <template x-for="(message, index) in messages" :key="index">
          <div
            :class="{
              'self-start max-w-[70%]': message.role === 'assistant',
              'self-end max-w-[70%]': message.role === 'user'
            }"
            class="message-container"
          >
            <div
              :class="{
              'bg-[#1E232E] text-[#F8F8FA]': message.role === 'assistant',
              'bg-[#F8F8FA] border border-[#DCDCDC]': message.role === 'user'
            }"
              class="p-3 rounded-lg break-words"
            >
              <span x-text="message.content"></span>
            </div>
          </div>
        </template>
      </div>

      <!-- Typing indicator positioned absolutely -->
      <div
        x-show="isTyping"
        x-cloak
        class="typing-indicator bg-gray-200 p-3 rounded-lg flex space-x-1 items-center max-w-[70%] absolute"
        id="typing-indicator"
      >
        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
        <div
          class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
          style="animation-delay: 0.2s"
        >
        </div>
        <div
          class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
          style="animation-delay: 0.4s"
        >
        </div>
      </div>
    </div>

    <!-- Chat Suggestions -->
    <div
      x-show="showSuggestions && messages.length === 1"
      x-transition:enter="transition ease-out duration-300 delay-300"
      x-transition:enter-start="opacity-0 transform translate-y-4"
      x-transition:enter-end="opacity-100 transform translate-y-0"
      x-transition:leave="transition ease-in duration-200"
      x-transition:leave-start="opacity-100 transform translate-y-0"
      x-transition:leave-end="opacity-0 transform translate-y-4"
      class="px-4 pb-2 space-y-2 suggestions-container"
    >
      <button
        class="w-full text-left p-3 bg-[#E6F3FF] hover:bg-[#D1E9FF] text-[#2D3748] rounded-lg border border-[#B8E0FF] transition-colors duration-200 text-sm font-medium suggestion-box"
      >
        💰 Explain pricing options
      </button>
      <button
        class="w-full text-left p-3 bg-[#E6F3FF] hover:bg-[#D1E9FF] text-[#2D3748] rounded-lg border border-[#B8E0FF] transition-colors duration-200 text-sm font-medium suggestion-box"
      >
        ⚙️ How does it work?
      </button>
    </div>

    <!-- Input Area -->
    <div class="flex items-center px-2 py-2 border-t border-[#DCDCDC]">
      <div class="flex-1 relative">
        <input type="text"
          x-model="currentTypingText"
          placeholder="Type a message..."
          readonly
          class="w-full px-3 py-2 pr-10 text-sm rounded-md bg-[#F8F8FA] border border-[#DCDCDC] focus:outline-none focus:ring-2 focus:ring-[#3D84F3] text-[#1E232E] cursor-default"
          style="overflow-x: auto; white-space: nowrap; text-overflow: ellipsis;">
        <button type="button"
          disabled
          class="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-[#1E232E] opacity-50 cursor-not-allowed">
          <img src="/src/assets/send_chat.svg" alt="Send" width="22" />
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('alpine:init', () => {
    Alpine.data('chatDemo', () => ({
      open: true,
      message: '',
      messages: [{ role: 'assistant', content: 'Hey there! 👋 How can I assist you today?' }],
      isTyping: false,
      showSuggestions: true,
      demoStarted: false,
      currentTypingText: '',

      // Demo conversation data
      demoConversation: [
        {
          role: 'user',
          content: 'What pricing options do you offer?',
          delay: 3000,
          typingDuration: 1200
        },
        {
          role: 'assistant',
          content: 'We have several plans starting at $99/month. Our Starter plan includes AI chat, basic customization, and up to 500 conversations. The Professional plan at $249/month adds advanced features and analytics. Would you like me to explain the features of each plan?',
          delay: 1800,
          typingIndicatorDuration: 2000
        },
        {
          role: 'user',
          content: 'Yes, please tell me more about the Professional plan features.',
          delay: 2500,
          typingDuration: 1800
        },
        {
          role: 'assistant',
          content: 'The Professional plan includes everything in Starter plus: advanced customization options, up to 2,000 conversations per month, priority support, and a comprehensive analytics dashboard to track performance. Perfect for growing businesses that need more robust features. Would you like to schedule a demo?',
          delay: 1800,
          typingIndicatorDuration: 2200
        },
        {
          role: 'user',
          content: 'That sounds great! How do I get started?',
          delay: 2000,
          typingDuration: 1000
        },
        {
          role: 'assistant',
          content: 'Excellent! You can get started by clicking the button below to contact us, or check out our pricing page for more details. We will help you set up your custom AI chat widget in no time! 🚀',
          delay: 1500,
          typingIndicatorDuration: 1800
        }
      ],

      init() {
        // Component initialization
      },

      startDemo() {
        if (this.demoStarted) return;
        this.demoStarted = true;

        // Show initial message after a delay
        setTimeout(() => {
          this.animateMessage(this.$refs.messages.querySelector('.message-container'));

          // Show suggestions after initial message
          setTimeout(() => {
            this.showSuggestions = true;
          }, 800);

          // Start the conversation sequence
          this.runConversationSequence();
        }, 1000);
      },

      runConversationSequence() {
        let currentDelay = 2000; // Start after 2 seconds

        this.demoConversation.forEach((message, index) => {
          currentDelay += message.delay;

          if (message.role === 'user') {
            setTimeout(() => {
              // Hide suggestions when user starts typing
              if (index === 0) {
                this.showSuggestions = false;
              }

              // Simulate user typing
              this.simulateUserTyping(message.content, message.typingDuration || 1500, () => {
                this.messages.push({ role: 'user', content: message.content });
                this.$nextTick(() => {
                  const newMessage = this.$refs.messages.querySelector('.message-container:last-child');
                  if (newMessage) this.animateMessage(newMessage);
                  this.scrollToBottom();
                });
              });
            }, currentDelay);
            currentDelay += (message.typingDuration || 1500) + 500;
          } else {
            // Assistant message
            setTimeout(() => {
              this.isTyping = true;
              setTimeout(() => {
                this.isTyping = false;
                this.messages.push({ role: 'assistant', content: message.content });
                this.$nextTick(() => {
                  const newMessage = this.$refs.messages.querySelector('.message-container:last-child');
                  if (newMessage) this.animateMessage(newMessage);
                  this.scrollToBottom();
                });
              }, message.typingIndicatorDuration || 1500);
            }, currentDelay);
            currentDelay += (message.typingIndicatorDuration || 1500) + 500;
          }
        });
      },

      simulateUserTyping(text, duration, callback) {
        this.currentTypingText = '';
        let currentIndex = 0;
        const typingSpeed = duration / text.length;

        const typeInterval = setInterval(() => {
          if (currentIndex < text.length) {
            this.currentTypingText += text[currentIndex];
            currentIndex++;
          } else {
            clearInterval(typeInterval);
            setTimeout(() => {
              this.currentTypingText = '';
              if (callback) callback();
            }, 500);
          }
        }, typingSpeed);
      },

      animateMessage(element) {
        if (!element) return;

        // Simple CSS animation fallback
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px) scale(0.9)';

        setTimeout(() => {
          element.style.transition = 'all 0.5s ease-out';
          element.style.opacity = '1';
          element.style.transform = 'translateY(0) scale(1)';
        }, 50);
      },

      scrollToBottom() {
        this.$nextTick(() => {
          if (this.$refs.messages) {
            this.$refs.messages.scrollTop = this.$refs.messages.scrollHeight;
          }
        });
      }
    }));
  });
</script>


<style>
  /* Smooth scrolling for messages container */
  [x-ref="messages"] {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Enhanced message container styling for animations */
  .message-container {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    /* Start with invisible state for animation */
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }

  /* Typing indicator enhanced styling */
  .typing-indicator {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    z-index: 10;
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    position: absolute;
  }

  /* Suggestion boxes styling */
  .suggestion-box {
    will-change: transform, opacity;
    backface-visibility: hidden;
    transform: translateZ(0);
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  /* Animation for the bouncing dots */
  .animate-bounce {
    animation: bounce 1s infinite;
  }

  @keyframes bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  /* Hide content until Alpine.js is loaded */
  [x-cloak] {
    display: none !important;
  }

  /* Scrollbar hiding */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 767px) {
    .suggestions-container {
      padding-left: 12px;
      padding-right: 12px;
    }

    .suggestion-box {
      padding: 12px 16px;
      font-size: 0.875rem;
    }
  }
</style>

