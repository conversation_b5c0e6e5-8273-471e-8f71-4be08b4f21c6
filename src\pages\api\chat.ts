// src/pages/api/chat.ts
import type { APIRoute } from 'astro';

// This ensures the API route is server-rendered, not static
export const prerender = false;

// Add model configuration
const MODEL_CONFIGS = {
  0: {
    name: "meta-llama/Meta-Llama-3.3-8B-Instruct",
    envKey: "OPENROUTER_API_KEY"
  },
  1: {
    name: "deepseek/deepseek-r1-0528",
    envKey: "OPENROUTER_API_KEY"
  },
  2: {
    name: "deepseek/deepseek-r1-0528-qwen3-8b",
    envKey: "OPENROUTER_API_KEY"
  }
};

// Enhanced system message with form assistance and Calendly integration
const CLEARBEAM_SYSTEM_MESSAGE = {
  role: 'system',
  content: `You are a Clearbeam sales assistant with advanced form assistance and appointment scheduling capabilities. Tone: Friendly, concise, persuasive like a top-performing sales rep. Goal: Maximize lead conversion, demo bookings, and provide excellent form assistance.

CRITICAL FORMATTING RULES - FOLLOW EXACTLY:
- NO markdown formatting whatsoever
- NO asterisks (*), NO hashes (#), NO underscores (_)  
- NO bullet points, NO numbered lists
- NO headers or bold text formatting
- Use plain text only with normal punctuation
- Links format: [Text](URL) - example: [Contact us](https://clearbeam.tech/contact)

RESPONSE LENGTH RULES:
- Default: Short responses only (1-2 lines max)
- Only go longer when user specifically asks for breakdowns, comparisons, or details
- Always get to the point fast

WHAT CLEARBEAM DOES:
Clearbeam sets up custom AI chat widgets for your website that answer questions 24/7, capture leads instantly, walk users through forms or scheduling, feel personal and branded to your site, and are configured plus installed for you with no coding required.

PRICING DETAILS:
Setup Fees (One-time):
- Starter Setup: $399 (Custom AI widget design, light branding, basic integration, response setup, email support)
- Pro Setup: $799 (Everything in Starter plus full branding & deep customization, analytics + dashboard prep, form + calendar integration, multi-step workflows, ongoing consultation)

Monthly Maintenance:
- Core: $29/month (Hosting, monitoring, basic updates, standard support)
- Plus: $69/month (Everything in Core plus priority support, custom feature requests, AI prompt tuning, analytics reports, monthly optimization calls)

Add-ons available: CRM Integration, Email triggers, External API logic, Additional site widgets. Pricing is modular and customizable.

FORM ASSISTANCE CAPABILITIES:
You can help users fill out the demo request form with these fields:
- firstName (required): User's first name
- lastName (required): User's last name  
- email (required): Valid email address
- phone (required): Phone number
- company (required): Company name
- website (required): Website URL (must include https://)
- businessType (required): E-commerce, SaaS/Software, Local Services, Professional Services, Healthcare, Real Estate, Education, Non-profit, Other
- monthlyTraffic (required): Under 1k, 1k-5k, 5k-10k, 10k-25k, 25k-50k, 50k-100k, Over 100k visitors
- challenges (required): Current customer support challenges
- goals (required): What they want to achieve with AI chat widget
- budget (required): Under $500, $500-1k, $1k-2.5k, $2.5k-5k, Over $5k, Flexible based on value
- timeline (required): ASAP, Within a week, Within a month, Within 3 months, Just exploring

FORM ASSISTANCE WORKFLOW:
1. When user asks for form help, ask if they want to fill it out themselves or get assistance
2. If they want assistance, collect information conversationally one field at a time
3. Always validate information (especially email format, website URLs)
4. If they have questions mid-form, answer them and offer: "🡩 Return to Form" to continue
5. After form completion, offer to schedule a call

APPOINTMENT SCHEDULING:
- After form completion or when user shows interest, offer to schedule a discovery call
- Ask about preferred time/day preferences
- Mention we offer 15-minute discovery calls to discuss their needs
- Use action phrases like "Would you like me to help you schedule a call with our team?"
- If they want to schedule a call, offer to schedule a call, and show the button to schedule a call with calendly.
- When confirmed, provide scheduling link or trigger calendar modal

CRITICAL REMINDERS:
- Keep responses short and punchy
- Always end with a call to action when appropriate
- Never use any formatting characters
- Always get to the point fast
- Help users navigate between form and chat seamlessly
- Proactively offer scheduling after successful form interactions`
};

export const POST: APIRoute = async ({ request }) => {
  console.log('Chat API endpoint called');
  
  try {
    // Validate request
    if (!request.body) {
      console.log('Request body is missing');
      return new Response(JSON.stringify({ error: 'Request body is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    console.log('Request body received:', body);
    
    // Get model selection from request or use default
    const modelIndex = body.modelIndex !== undefined ? Number(body.modelIndex) : 2; // Default to Qwen3 8B
    const modelConfig = MODEL_CONFIGS[modelIndex] || MODEL_CONFIGS[2]; // Fallback to default if invalid
    
    const apiKey = import.meta.env[modelConfig.envKey];
    console.log('Using model:', modelConfig.name);
    console.log('API key available:', !!apiKey);
    
    // Validate API key
    if (!apiKey) {
      console.error(`${modelConfig.envKey} is not configured`);
      return new Response(JSON.stringify({ 
        error: 'API configuration error',
        choices: [{ message: { content: 'Service temporarily unavailable. Please try again later.' } }]
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Prepare messages array - always ensure system message is first
    let messages = body.messages || [];
    
    // Validate messages array
    if (!Array.isArray(messages)) {
      console.log('Invalid messages format:', messages);
      return new Response(JSON.stringify({ 
        error: 'Invalid messages format',
        choices: [{ message: { content: 'Please provide a valid message.' } }]
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Remove any existing system messages and add our enhanced system message at the beginning
    messages = messages.filter(msg => msg.role !== 'system');
    messages.unshift(CLEARBEAM_SYSTEM_MESSAGE);
    
    // Add form context if in form mode
    if (body.formMode && body.formData) {
      messages.push({
        role: 'system',
        content: `FORM CONTEXT: User is currently working on the demo request form. Current form data: ${JSON.stringify(body.formData)}. Help them complete the form or answer questions about it.`
      });
    }
    
    console.log('Final messages array:', JSON.stringify(messages, null, 2));
    
    if (messages.length === 0) {
      console.log('No messages after processing');
      return new Response(JSON.stringify({ 
        error: 'No messages to process',
        choices: [{ message: { content: 'Please provide a message.' } }]
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const payload = {
      messages,
      model: modelConfig.name,
      max_tokens: 1000,
      temperature: 0.7,
      stream: false // Explicitly set to false for simpler handling
    };
    
    console.log('Sending payload to OpenRouter:', JSON.stringify(payload, null, 2));

    const res = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': request.headers.get('referer') || 'http://localhost:4321', // OpenRouter likes this
        'X-Title': 'ChatWidget' // Optional: helps with OpenRouter analytics
      },
      body: JSON.stringify(payload),
    });

    console.log('OpenRouter response status:', res.status);
    
    if (!res.ok) {
      console.error('OpenRouter API error:', res.status, res.statusText);
      
      // Try to get error details
      let errorMessage = 'AI service error';
      try {
        const errorData = await res.json();
        console.error('OpenRouter error details:', errorData);
        errorMessage = errorData.error?.message || errorMessage;
      } catch (e) {
        // Ignore JSON parsing error
        console.error('Failed to parse error response');
      }
      
      return new Response(JSON.stringify({ 
        error: errorMessage,
        choices: [{ message: { content: 'Sorry, I encountered an error. Please try again in a moment.' } }]
      }), {
        status: res.status,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const data = await res.json();
    console.log('OpenRouter response data:', data);
    
    // Validate response structure
    if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
      console.error('Invalid API response structure:', data);
      return new Response(JSON.stringify({ 
        error: 'Invalid response from AI service',
        choices: [{ message: { content: 'Sorry, I received an invalid response. Please try again.' } }]
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    console.log('Returning successful response to client');
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache' // Prevent caching of AI responses
      },
    });
    
  } catch (error) {
    console.error('Chat API error:', error);
    
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      choices: [{ message: { content: 'Sorry, something went wrong. Please try again later.' } }]
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};