---
const steps = [
  {
    number: "01",
    title: "Consultation",
    description: "We discuss your goals, branding requirements, and integration needs to create the perfect solution for your business.",
    icon: "edit_note"
  },
  {
    number: "02", 
    title: "Design & Build",
    description: "Our team creates your AI assistant with feedback cycles, ensuring it matches your brand and meets your specific requirements.",
    icon: "palette"
  },
  {
    number: "03",
    title: "Integration",
    description: "We embed the widget on your website with full configuration support and testing to ensure everything works perfectly.",
    icon: "build"
  }
];

const techStack = [
  {
    name: "Vercel",
    description: "Reliable hosting platform",
    icon: "cloud"
  },
  {
    name: "OpenRouter",
    description: "Advanced AI capabilities",
    icon: "psychology"
  },
  {
    name: "Calend<PERSON>",
    description: "Seamless scheduling",
    icon: "event"
  }
];
---

<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

<section class="py-20 bg-white">
  <div class="container mx-auto px-4">
    <h2 class="text-4xl font-bold text-center text-[#1E232E] mb-4">How It Works</h2>
    <p class="text-xl text-center text-gray-600 mb-16 max-w-2xl mx-auto">
      A simple, proven process to get your AI assistant up and running
    </p>

    <div class="relative max-w-4xl mx-auto">
      <!-- Connection Line -->
      <div class="absolute left-1/2 top-0 w-px h-full bg-[#3D84F3] hidden lg:block" style="transform: translateX(-50%);"></div>

      <!-- Steps -->
      <div class="space-y-16 relative">
        {steps.map((step, index) => (
          <div class={`flex flex-col lg:flex-row gap-8 items-center ${index % 2 === 0 ? '' : 'lg:flex-row-reverse'}`}>
            <div class={`flex-1 text-center ${index % 2 === 0 ? 'lg:text-right' : 'lg:text-left'}`}>
              <div class={`inline-block mb-6 ${index % 2 === 0 ? 'lg:ml-auto' : ''}`}>
                <span class="material-icons text-[#3D84F3]" style="font-size: 3.5rem;">{step.icon}</span>
              </div>
              <h3 class="text-2xl font-bold text-[#1E232E] mb-4">
                <span class="text-[#3D84F3]">{step.number}.</span> {step.title}
              </h3>
              <p 
                class={`text-gray-600 text-lg leading-relaxed max-w-md mx-auto ${index % 2 === 0 ? 'lg:mx-0 lg:ml-auto lg:text-right' : 'lg:mx-0 lg:mt-2 lg:text-left'}`}
              >
                {step.description}
              </p>
            </div>
            
            <div class="w-20 h-20 rounded-full bg-[#3D84F3] text-white flex items-center justify-center text-2xl font-bold relative z-10 order-first lg:order-none shadow-lg">
              {step.number}
            </div>
            
            <div class="flex-1"></div>
          </div>
        ))}
      </div>
    </div>

    <!-- Tech Stack Section -->
    <div class="mt-20 pt-16 border-t border-gray-200 pb-[5vh]">
      <h3 class="text-2xl font-bold text-center text-[#1E232E] mb-4">Powered by Industry Leaders</h3>
      <p class="text-center text-gray-600 mb-12">Built on reliable, cutting-edge technology</p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
        {techStack.map((tech) => (
          <div class="text-center p-6 bg-[#F7F8F9] rounded-lg hover:shadow-md transition-shadow duration-300">
            <span class="material-icons text-[#3D84F3] mb-4" style="font-size: 3rem;">{tech.icon}</span>
            <h4 class="text-xl font-bold text-[#1E232E] mb-2">{tech.name}</h4>
            <p class="text-gray-600">{tech.description}</p>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>