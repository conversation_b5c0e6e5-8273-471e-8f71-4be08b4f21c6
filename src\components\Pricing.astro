---
// Enhanced Pricing.astro with setup fees and monthly maintenance plans
const setupPlans = [
  {
    name: "Starter Setup",
    price: "$399",
    period: "one-time",
    description: "Perfect for small businesses getting started with AI assistance.",
    features: [
      "Custom AI widget design",
      "Light branding",
      "Basic integration",
      "Response setup",
      "Email support during setup"
    ],
    cta: "Get Started",
    popular: false,
    type: "setup"
  },
  {
    name: "Pro Setup",
    price: "$799",
    period: "one-time",
    description: "Complete solution for businesses wanting full customization.",
    features: [
      "Everything in Starter Setup",
      "Full branding & deep customization",
      "Analytics + dashboard prep",
      "Form + calendar integration",
      "Multi-step workflows",
      "Ongoing consultation during build"
    ],
    cta: "Get Started",
    popular: true,
    type: "setup"
  }
];

const monthlyPlans = [
  {
    name: "Core",
    price: "$29",
    period: "per month",
    description: "Essential maintenance and support for your AI widget.",
    features: [
      "Hosting & uptime monitoring",
      "Basic updates & maintenance",
      "Standard support",
      "Performance monitoring",
      "Security updates"
    ],
    cta: "Choose Plan",
    popular: false,
    type: "monthly"
  },
  {
    name: "Plus",
    price: "$69",
    period: "per month",
    description: "Advanced support with custom features and optimization.",
    features: [
      "Everything in Core",
      "Priority support",
      "Custom feature requests",
      "AI prompt tuning",
      "Analytics reports",
      "Monthly optimization calls"
    ],
    cta: "Choose Plan",
    popular: true,
    type: "monthly"
  }
];

const addOns = [
  "CRM Integration (Notion, HubSpot, Airtable, etc.)",
  "Email triggers & automation",
  "External API logic",
  "Additional site widgets",
  "Advanced analytics dashboard",
  "Custom workflow automation"
];
---

<section id="pricing" class="py-16 md:py-20 bg-[#F7F8F9] px-4 md:px-6 overflow-x-hidden">
  <div class="max-w-7xl mx-auto">
    <h2 class="text-3xl md:text-4xl font-bold text-center text-[#20232D] mb-4">Transparent Pricing</h2>
    <p class="text-center text-[#20232D] mb-12 max-w-3xl mx-auto">
      Our pricing is modular and customizable based on your business needs. Start with a one-time setup, then choose your ongoing maintenance plan.
    </p>
    
    <!-- Setup Fees Section -->
    <div class="mb-16">
      <h3 class="text-2xl md:text-3xl font-bold text-center text-[#20232D] mb-3">One-Time Setup Fee</h3>
      <p class="text-center text-gray-600 mb-8">Development, Design & Configuration</p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {setupPlans.map((plan) => (
          <div class={`bg-white rounded-lg shadow-lg overflow-hidden border flex flex-col h-full ${plan.popular ? 'border-[#3D84F3] relative' : 'border-gray-200'}`}>
            {plan.popular && (
              <div class="bg-[#3D84F3] text-white text-center py-2 text-sm font-medium">
                Most Popular
              </div>
            )}
            
            <div class="p-6 flex-grow flex flex-col">
              <h4 class="text-xl font-bold text-[#20232D]">{plan.name}</h4>
              <div class="mt-4 flex items-baseline">
                <span class="text-4xl font-extrabold text-[#20232D]">{plan.price}</span>
                <span class="ml-2 text-lg text-gray-500">{plan.period}</span>
              </div>
              <p class="mt-4 text-gray-600">{plan.description}</p>
              
              <ul class="mt-6 space-y-3 flex-grow">
                {plan.features.map((feature) => (
                  <li class="flex items-start">
                    <svg class="h-5 w-5 text-[#3D84F3] mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-3 text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <div class="mt-8">
                <a href="/contact" class={`block w-full py-3 px-4 rounded-md text-center font-medium transition-colors duration-200 ${plan.popular ? 'bg-[#3D84F3] text-white hover:bg-[#2D74E3]' : 'text-[#20232D] border border-[#20232D] hover:bg-[#F0F0F0]'}`}>
                  {plan.cta}
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>

    <!-- Monthly Plans Section -->
    <div class="mb-16">
      <h3 class="text-2xl md:text-3xl font-bold text-center text-[#20232D] mb-3">Monthly Maintenance Plans</h3>
      <p class="text-center text-gray-600 mb-8">Ongoing support and optimization</p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {monthlyPlans.map((plan) => (
          <div class={`bg-white rounded-lg shadow-lg overflow-hidden border flex flex-col h-full ${plan.popular ? 'border-[#3D84F3] relative' : 'border-gray-200'}`}>
            {plan.popular && (
              <div class="bg-[#3D84F3] text-white text-center py-2 text-sm font-medium">
                Recommended
              </div>
            )}
            
            <div class="p-6 flex-grow flex flex-col">
              <h4 class="text-xl font-bold text-[#20232D]">{plan.name}</h4>
              <div class="mt-4 flex items-baseline">
                <span class="text-4xl font-extrabold text-[#20232D]">{plan.price}</span>
                <span class="ml-2 text-lg text-gray-500">{plan.period}</span>
              </div>
              <p class="mt-4 text-gray-600">{plan.description}</p>
              
              <ul class="mt-6 space-y-3 flex-grow">
                {plan.features.map((feature) => (
                  <li class="flex items-start">
                    <svg class="h-5 w-5 text-[#3D84F3] mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-3 text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <div class="mt-8">
                <a href="/contact" class={`block w-full py-3 px-4 rounded-md text-center font-medium transition-colors duration-200 ${plan.popular ? 'bg-[#3D84F3] text-white hover:bg-[#2D74E3]' : 'text-[#20232D] border border-[#20232D] hover:bg-[#F0F0F0]'}`}>
                  {plan.cta}
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>

    <!-- Add-ons Section -->
    <div class="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto">
      <h3 class="text-2xl font-bold text-center text-[#20232D] mb-6">Add-ons  </h3>
      <p class="text-center text-gray-600 mb-8">Enhance your AI widget with additional features</p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        {addOns.map((addon) => (
          <div class="flex items-center p-4 bg-[#F7F8F9] rounded-lg">
            <svg class="h-5 w-5 text-[#3D84F3] mr-3 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <span class="text-gray-700">{addon}</span>
          </div>
        ))}
      </div>
      
      <div class="text-center mt-8">
        <p class="text-gray-600 mb-4">Pricing varies based on complexity and requirements</p>
        <a href="/contact" class="inline-block bg-[#1E232E] text-white py-3 px-8 rounded-lg hover:bg-[#33384D] transition-colors duration-300 font-medium">
          Get Custom Quote
        </a>
      </div>
    </div>
  </div>
</section>